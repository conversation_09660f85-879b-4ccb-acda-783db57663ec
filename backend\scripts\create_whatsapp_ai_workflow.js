const axios = require('axios');

// Configuración
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZjM0ODNlMC0xMjViLTRjMGItYTI4ZS1mNGJkMTJlYTdmNzAiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4MzI2Mzg0fQ.YHpG_nNe3ssUgOT7JmYm4a5o8VV221oQZHM3F4bIzzM';

// Workflow principal de WhatsApp con IA
async function createWhatsAppAIWorkflow() {
  const workflowData = {
    name: 'WhatsApp AI Message Processor',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir mensajes de WhatsApp
      {
        parameters: {
          httpMethod: 'POST',
          path: 'whatsapp-message',
          responseMode: 'responseNode',
          options: {}
        },
        id: 'webhook-whatsapp',
        name: 'WhatsApp Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },

      // 2. Guardar mensaje en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.from }}',
              texto_mensaje: '={{ $json.body }}',
              fecha_recepcion: '={{ new Date().toISOString() }}',
              institucion_id: 1,
              procesado: false
            }
          },
          table: 'mensajes_whatsapp',
          dataMode: 'defineBelow'
        },
        id: 'save-message',
        name: 'Guardar Mensaje',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // 3. Análisis con IA (usando Code node para simular IA)
      {
        parameters: {
          jsCode: `
// Análisis de IA del mensaje
const message = $input.first().json.body.toLowerCase();
const phone = $input.first().json.from;

let analysis = {
  type: 'general',
  intent: 'unknown',
  confidence: 0.5,
  requires_director_attention: false,
  auto_response: null,
  extracted_data: {}
};

// Detectar ausencias
const absenceKeywords = ['ausencia', 'falta', 'no va', 'no ira', 'no asistira', 'enfermo', 'no puede ir'];
if (absenceKeywords.some(keyword => message.includes(keyword))) {
  analysis.type = 'absence';
  analysis.intent = 'report_absence';
  analysis.confidence = 0.9;
  analysis.auto_response = 'Entendido. Para procesar la ausencia, necesito el nombre completo del estudiante y su grado. ¿Podrías proporcionarme esta información?';
  
  // Extraer nombre si está presente
  const nameMatch = message.match(/(?:mi hijo|mi hija|el estudiante|la estudiante)\\s+([a-záéíóúñ\\s]+)/i);
  if (nameMatch) {
    analysis.extracted_data.student_name = nameMatch[1].trim();
  }
}

// Detectar preguntas sobre horarios
else if (message.includes('hay clases') || message.includes('horario')) {
  analysis.type = 'question';
  analysis.intent = 'schedule_inquiry';
  analysis.confidence = 0.8;
  analysis.auto_response = 'Sí, hay clases normalmente. El horario es de 7:00 AM a 12:00 PM de lunes a viernes.';
}

// Detectar preguntas sobre el director
else if (message.includes('director') || message.includes('donde esta')) {
  analysis.type = 'director_inquiry';
  analysis.intent = 'director_request';
  analysis.confidence = 0.9;
  analysis.requires_director_attention = true;
  analysis.auto_response = 'Un momento por favor, estoy notificando al director sobre su consulta.';
}

// Detectar quejas o problemas urgentes
else if (message.includes('problema') || message.includes('queja') || message.includes('urgente')) {
  analysis.type = 'complaint';
  analysis.intent = 'urgent_matter';
  analysis.confidence = 0.85;
  analysis.requires_director_attention = true;
  analysis.auto_response = 'Entiendo su preocupación. He notificado al director y se pondrá en contacto con usted pronto.';
}

return [{
  json: {
    ...analysis,
    original_message: $input.first().json.body,
    phone_number: phone,
    message_id: $('save-message').first().json.id,
    timestamp: new Date().toISOString()
  }
}];
`
        },
        id: 'ai-analysis',
        name: 'Análisis IA',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [680, 300]
      },

      // 4. Actualizar mensaje con análisis IA
      {
        parameters: {
          operation: 'update',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              ai_analysis: '={{ JSON.stringify($json) }}',
              message_type: '={{ $json.type }}',
              requires_attention: '={{ $json.requires_director_attention }}'
            }
          },
          table: 'mensajes_whatsapp',
          updateKey: 'id',
          columnToMatchOn: 'id',
          valueToMatchOn: '={{ $json.message_id }}'
        },
        id: 'update-analysis',
        name: 'Actualizar Análisis',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [900, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // 5. Decisión: ¿Requiere atención del director?
      {
        parameters: {
          conditions: {
            options: {
              caseSensitive: true,
              leftValue: '',
              typeValidation: 'strict'
            },
            conditions: [
              {
                id: 'condition1',
                leftValue: '={{ $json.requires_director_attention }}',
                rightValue: true,
                operator: {
                  type: 'boolean',
                  operation: 'equal'
                }
              }
            ],
            combinator: 'and'
          }
        },
        id: 'needs-director',
        name: '¿Requiere Director?',
        type: 'n8n-nodes-base.if',
        typeVersion: 2,
        position: [1120, 300]
      },

      // 6. Notificar al Director (WhatsApp)
      {
        parameters: {
          url: 'https://api.whatsapp.com/send',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'phone',
                value: '+50312345678' // Número del director
              },
              {
                name: 'text',
                value: '🔔 ATENCIÓN REQUERIDA\\n\\nNuevo mensaje de: {{ $json.phone_number }}\\nTipo: {{ $json.type }}\\nMensaje: {{ $json.original_message }}\\n\\nPor favor revise el dashboard.'
              }
            ]
          },
          options: {}
        },
        id: 'notify-director',
        name: 'Notificar Director',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [1340, 200]
      },

      // 7. Decisión: ¿Es ausencia?
      {
        parameters: {
          conditions: {
            options: {
              caseSensitive: true,
              leftValue: '',
              typeValidation: 'strict'
            },
            conditions: [
              {
                id: 'condition1',
                leftValue: '={{ $json.type }}',
                rightValue: 'absence',
                operator: {
                  type: 'string',
                  operation: 'equals'
                }
              }
            ],
            combinator: 'and'
          }
        },
        id: 'is-absence',
        name: '¿Es Ausencia?',
        type: 'n8n-nodes-base.if',
        typeVersion: 2,
        position: [1340, 400]
      },

      // 8. Procesar Ausencia
      {
        parameters: {
          jsCode: `
// Procesar ausencia automáticamente
const data = $input.first().json;

// Buscar si ya existe un proceso de ausencia para este teléfono
// (Aquí simularemos el proceso)

let response = {
  action: 'start_absence_process',
  student_name: data.extracted_data.student_name || null,
  needs_more_info: !data.extracted_data.student_name,
  phone_number: data.phone_number,
  message_id: data.message_id
};

if (response.needs_more_info) {
  response.reply_message = 'Para procesar la ausencia, necesito el nombre completo del estudiante y su grado. ¿Podrías proporcionarme esta información?';
} else {
  response.reply_message = 'Perfecto. He registrado la ausencia de ' + response.student_name + '. Se notificará al docente correspondiente.';
  response.action = 'create_absence_report';
}

return [{ json: response }];
`
        },
        id: 'process-absence',
        name: 'Procesar Ausencia',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1560, 500]
      },

      // 9. Crear Reporte de Ausencia en BD
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              fecha_ausencia: '={{ new Date().toISOString().split("T")[0] }}',
              motivo: 'Reportado por WhatsApp: {{ $json.original_message }}',
              reportado_por_telefono: '={{ $json.phone_number }}',
              grado_reportado: '{{ $json.extracted_data.grade || "Por confirmar" }}',
              justificado: true,
              notificado_docente: false
            }
          },
          table: 'ausencias',
          dataMode: 'defineBelow'
        },
        id: 'create-absence',
        name: 'Crear Ausencia',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1780, 500],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // 10. Generar PDF del Reporte
      {
        parameters: {
          jsCode: `
// Generar contenido del PDF
const data = $input.first().json;

const pdfContent = {
  title: 'REPORTE DE AUSENCIA',
  content: \`
INSTITUCIÓN: Colegio Cristiano Jerusalén de los Altos de Palencia

REPORTE DE AUSENCIA ESTUDIANTIL

Fecha del reporte: \${new Date().toLocaleDateString()}
Hora del reporte: \${new Date().toLocaleTimeString()}

INFORMACIÓN DEL ESTUDIANTE:
- Nombre: \${data.student_name || 'Por confirmar'}
- Grado: \${data.extracted_data?.grade || 'Por confirmar'}

INFORMACIÓN DEL REPORTE:
- Reportado por: Padre de familia
- Teléfono: \${data.phone_number}
- Fecha de ausencia: \${new Date().toLocaleDateString()}
- Motivo: \${data.original_message}

ESTADO: Justificado
NOTIFICACIÓN DOCENTE: Pendiente

---
Este reporte fue generado automáticamente por el sistema de gestión escolar.
\`
};

return [{ json: { ...data, pdf_content: pdfContent } }];
`
        },
        id: 'generate-pdf',
        name: 'Generar PDF',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [2000, 500]
      },

      // 11. Enviar respuesta automática
      {
        parameters: {
          url: 'https://api.whatsapp.com/send',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'phone',
                value: '={{ $json.phone_number }}'
              },
              {
                name: 'text',
                value: '={{ $json.auto_response || "Mensaje recibido correctamente." }}'
              }
            ]
          },
          options: {}
        },
        id: 'send-auto-response',
        name: 'Respuesta Automática',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [1340, 600]
      },

      // 12. Notificar Dashboard (WebSocket)
      {
        parameters: {
          url: 'http://backend:3001/api/dashboard/notify',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'type',
                value: 'new_whatsapp_message'
              },
              {
                name: 'message',
                value: '={{ $json }}'
              }
            ]
          },
          options: {}
        },
        id: 'notify-dashboard',
        name: 'Notificar Dashboard',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [2220, 300]
      },

      // 13. Responder al webhook
      {
        parameters: {
          respondWith: 'json',
          responseBody: '{\n  "status": "processed",\n  "message": "Mensaje procesado correctamente por IA",\n  "analysis": {{ JSON.stringify($json) }}\n}'
        },
        id: 'webhook-response',
        name: 'Respuesta Webhook',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [2440, 300]
      }
    ],

    connections: {
      'WhatsApp Webhook': {
        main: [
          [
            {
              node: 'Guardar Mensaje',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Guardar Mensaje': {
        main: [
          [
            {
              node: 'Análisis IA',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Análisis IA': {
        main: [
          [
            {
              node: 'Actualizar Análisis',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Actualizar Análisis': {
        main: [
          [
            {
              node: '¿Requiere Director?',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      '¿Requiere Director?': {
        main: [
          [
            {
              node: 'Notificar Director',
              type: 'main',
              index: 0
            }
          ],
          [
            {
              node: '¿Es Ausencia?',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Notificar Director': {
        main: [
          [
            {
              node: '¿Es Ausencia?',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      '¿Es Ausencia?': {
        main: [
          [
            {
              node: 'Procesar Ausencia',
              type: 'main',
              index: 0
            }
          ],
          [
            {
              node: 'Respuesta Automática',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar Ausencia': {
        main: [
          [
            {
              node: 'Crear Ausencia',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Crear Ausencia': {
        main: [
          [
            {
              node: 'Generar PDF',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Generar PDF': {
        main: [
          [
            {
              node: 'Respuesta Automática',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Respuesta Automática': {
        main: [
          [
            {
              node: 'Notificar Dashboard',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Notificar Dashboard': {
        main: [
          [
            {
              node: 'Respuesta Webhook',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };

  try {
    const response = await axios.post(`${N8N_BASE_URL}/api/v1/workflows`, workflowData, {
      headers: {
        'Content-Type': 'application/json',
        'X-N8N-API-KEY': N8N_API_KEY
      }
    });

    console.log('✅ Workflow de WhatsApp AI creado exitosamente');
    console.log(`   ID: ${response.data.id}`);
    console.log(`   Webhook URL: ${N8N_BASE_URL}/webhook/whatsapp-message`);
    
    return response.data;
  } catch (error) {
    console.error('❌ Error creando workflow:', error.response?.data || error.message);
    throw error;
  }
}

// Función principal
async function createCompleteWhatsAppSystem() {
  try {
    console.log('=== Creando Sistema Completo de WhatsApp con IA ===');
    
    const workflow = await createWhatsAppAIWorkflow();
    
    console.log('\n=== SISTEMA CREADO EXITOSAMENTE ===');
    console.log('✅ Workflow de IA configurado');
    console.log('✅ Procesamiento automático de ausencias');
    console.log('✅ Notificaciones al director');
    console.log('✅ Generación de reportes PDF');
    console.log('✅ Integración con dashboard');
    
    console.log('\n📋 URLs importantes:');
    console.log(`   Webhook WhatsApp: ${N8N_BASE_URL}/webhook/whatsapp-message`);
    console.log(`   Editor n8n: ${N8N_BASE_URL}`);
    
    return workflow;
    
  } catch (error) {
    console.error('Error creando el sistema:', error);
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createCompleteWhatsAppSystem()
    .then(() => {
      console.log('\n🎉 Sistema completo creado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = { createCompleteWhatsAppSystem };
