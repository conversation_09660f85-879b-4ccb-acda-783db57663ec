{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;;;;;;;;;;AAgFlH,SAAS,+CAAyB,KAAqB;IACrD,8DAA8D;IAC9D,IAAI,UAAU,CAAA,GAAA,iBAAS,EAAE,CAAA,GAAA,yCAAoB;IAC7C,IAAI,SAAS;QACX,IAAI,YAAC,QAAQ,EAAE,GAAG,cAAa,GAAG;QAClC,QAAQ,CAAA,GAAA,iBAAS,EAAE,cAAc;QACjC;IACF;IACA,CAAA,GAAA,iBAAS,EAAE,SAAS,MAAM,GAAG;IAE7B,OAAO;AACT;IAYE;AAVF,MAAM;IAyCJ,sBAAsB;6BACf,8CAAyB;IAChC;IAEA,IAAI,wBAAwB;QAC1B,qBAAO,IAAI,EAAC;IACd;IAnCA,YAAY,IAAyB,EAAE,WAAwB,EAAE,aAAwB,EAAE,KAAkB,CAAE;QAF/G,qBAAA;;mBAAA,KAAA;;6BAAA,8CAAyB;YAGH;QAApB,IAAI,gBAAgB,CAAA,gBAAA,kBAAA,4BAAA,MAAO,MAAM,cAAb,2BAAA,gBAAiB,cAAc,aAAa;QAChE,MAAM,OAA6B,0BAAA,oCAAD,AAAC,cAA2B,qBAAqB;QACnF,IAAI,GAAG,IAAI;QACX,IAAI,SAAS,UAAyB;QACtC,IAAI,cAAc,OAAO,IAAI,QAAQ,cAAc,OAAO,IAAI,MAAM;YAClE,UAAU,cAAc,OAAO;YAC/B,UAAU,cAAc,OAAO;QACjC;QACA,IAAI;YACF,IAAI,WAAW,QAAQ,WAAW,MAAM;gBACtC,IAAI,UAAU,KAAK,IAAI;gBACvB,IAAI,UAAU,KAAK,GAAG;YACxB,OAAO;gBACL,IAAI,KAAK,KAAK,GAAG;gBACjB,IAAI,KAAK,MAAM,GAAG;YACpB;;QAEF,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG,cAAc,aAAa;QACzC,IAAI,CAAC,QAAQ,GAAG,cAAc,QAAQ;QACtC,IAAI,CAAC,OAAO,GAAG,cAAc,OAAO;QACpC,IAAI,CAAC,OAAO,GAAG,cAAc,OAAO;QACpC,IAAI,CAAC,MAAM,GAAG,cAAc,MAAM;QAClC,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;IACX;AASF;AAEA,MAAM,qCAAe,OAAO;AAC5B,MAAM,iCAAW;AACjB,MAAM,4CAAsB;AAOrB,SAAS,0CAAS,KAAqB;IAC5C,IAAI,WACF,OAAO,iBACP,aAAa,gBACb,YAAY,cACZ,UAAU,aACV,SAAS,WACT,OAAO,cACP,UAAU,EACV,WAAW,aAAa,uBACxB,mBAAmB,6BACnB,yBAAyB,6BACzB,yBAAyB,EACzB,KAAK,MAAM,EACX,GAAG,UACJ,GAAG,+CAAyB;IAE7B,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,GAAA,eAAO,EAAE;IACvC,IAAI,MAAM,CAAA,GAAA,aAAK,EAAc;QAC3B,WAAW;QACX,2BAA2B;QAC3B,mBAAmB;QACnB,mBAAmB;QACnB,iBAAiB;QACjB,QAAQ;QACR,cAAc;QACd,aAAa;QACb,aAAa,EAAE;IACjB;IAEA,IAAI,qBAAC,iBAAiB,4BAAE,wBAAwB,EAAC,GAAG,CAAA,GAAA,yBAAiB;IAErE,IAAI,oBAAoB,CAAA,GAAA,qBAAa,EAAE,CAAC,eAA0B;QAChE,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,cAAc,MAAM,iBAAiB,EACvC,OAAO;QAGT,IAAI,wBAAwB;QAC5B,MAAM,iBAAiB,GAAG;QAC1B,IAAI,cAAc;YAChB,IAAI,QAAQ,IAAI,iCAAW,cAAc,aAAa;YACtD,aAAa;YACb,wBAAwB,MAAM,qBAAqB;QACrD;QAEA,IAAI,eACF,cAAc;QAGhB,MAAM,iBAAiB,GAAG;QAC1B,MAAM,iBAAiB,GAAG;QAC1B,WAAW;QACX,OAAO;IACT;IAEA,IAAI,kBAAkB,CAAA,GAAA,qBAAa,EAAE,CAAC,eAA0B,aAA0B,aAAa,IAAI;QACzG,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,CAAC,MAAM,iBAAiB,EAC1B,OAAO;QAGT,MAAM,iBAAiB,GAAG;QAC1B,MAAM,iBAAiB,GAAG;QAE1B,IAAI,wBAAwB;QAC5B,IAAI,YAAY;YACd,IAAI,QAAQ,IAAI,iCAAW,YAAY,aAAa;YACpD,WAAW;YACX,wBAAwB,MAAM,qBAAqB;QACrD;QAEA,IAAI,eACF,cAAc;QAGhB,WAAW;QAEX,IAAI,WAAW,cAAc,CAAC,YAAY;YACxC,IAAI,QAAQ,IAAI,iCAAW,SAAS,aAAa;YACjD,QAAQ;YACR,0BAAA,wBAA0B,MAAM,qBAAqB;QACvD;QAEA,MAAM,iBAAiB,GAAG;QAC1B,OAAO;IACT;IAEA,IAAI,iBAAiB,CAAA,GAAA,qBAAa,EAAE,CAAC,eAA0B;QAC7D,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,YACF,OAAO;QAGT,IAAI,WAAW;YACb,MAAM,iBAAiB,GAAG;YAC1B,IAAI,QAAQ,IAAI,iCAAW,WAAW,aAAa;YACnD,UAAU;YACV,MAAM,iBAAiB,GAAG;YAC1B,OAAO,MAAM,qBAAqB;QACpC;QAEA,OAAO;IACT;IAEA,IAAI,SAAS,CAAA,GAAA,qBAAa,EAAE,CAAC;QAC3B,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,EAAE;YACnC,IAAI,MAAM,iBAAiB,IAAI,MAAM,WAAW,IAAI,MAClD,gBAAgB,kCAAY,MAAM,MAAM,EAAE,IAAI,MAAM,WAAW,EAAE;YAEnE,MAAM,SAAS,GAAG;YAClB,MAAM,YAAY,GAAG;YACrB,MAAM,eAAe,GAAG;YACxB,MAAM,WAAW,GAAG;YACpB;YACA,IAAI,CAAC,2BACH,CAAA,GAAA,yCAAmB,EAAE,MAAM,MAAM;YAEnC,KAAK,IAAI,WAAW,MAAM,WAAW,CACnC;YAEF,MAAM,WAAW,GAAG,EAAE;QACxB;IACF;IAEA,IAAI,sBAAsB,CAAA,GAAA,qBAAa,EAAE,CAAC;QACxC,IAAI,2BACF,OAAO;IAEX;IAEA,IAAI,eAAe,CAAA,GAAA,qBAAa,EAAE,CAAC;QACjC,oBAAA,8BAAA,QAAU;IACZ;IAEA,IAAI,wBAAwB,CAAA,GAAA,qBAAa,EAAE,CAAC,GAA+B;QACzE,iEAAiE;QACjE,yDAAyD;QACzD,4FAA4F;QAC5F,2CAA2C;QAC3C,+DAA+D;QAC/D,IAAI,SAAS;YACX,IAAI,QAAQ,IAAI,WAAW,SAAS;YACpC,CAAA,GAAA,yCAAa,EAAE,OAAO;YACtB,QAAQ,CAAA,GAAA,wCAAmB,EAAE;QAC/B;IACF;IAEA,IAAI,aAAa,CAAA,GAAA,cAAM,EAAE;QACvB,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,aAA4B;YAC9B,WAAU,CAAC;gBACT,IAAI,2CAAqB,EAAE,WAAW,EAAE,EAAE,aAAa,KAAK,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAAI;wBAwCtH;oBAvCF,IAAI,mDAA6B,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,GAAG,EAAE,GAAG,GACnE,EAAE,cAAc;oBAGlB,wEAAwE;oBACxE,0EAA0E;oBAC1E,wCAAwC;oBACxC,IAAI,wBAAwB;oBAC5B,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,EAAE,MAAM,EAAE;wBACjC,MAAM,MAAM,GAAG,EAAE,aAAa;wBAC9B,MAAM,SAAS,GAAG;wBAClB,MAAM,WAAW,GAAG;wBACpB,wBAAwB,kBAAkB,GAAG;wBAE7C,gFAAgF;wBAChF,2GAA2G;wBAC3G,wIAAwI;wBACxI,IAAI,iBAAiB,EAAE,aAAa;wBACpC,IAAI,UAAU,CAAC;4BACb,IAAI,2CAAqB,GAAG,mBAAmB,CAAC,EAAE,MAAM,IAAI,CAAA,GAAA,mBAAW,EAAE,gBAAgB,CAAA,GAAA,qBAAa,EAAE,OAAO,MAAM,MAAM,EACzH,eAAe,kCAAY,MAAM,MAAM,EAAE,IAAI;wBAEjD;wBAEA,kBAAkB,CAAA,GAAA,uBAAe,EAAE,EAAE,aAAa,GAAG,SAAS,CAAA,GAAA,YAAI,EAAE,SAAS,UAAU;oBACzF;oBAEA,IAAI,uBACF,EAAE,eAAe;oBAGnB,yFAAyF;oBACzF,+EAA+E;oBAC/E,0FAA0F;oBAC1F,iDAAiD;oBACjD,gEAAgE;oBAChE,gDAAgD;oBAChD,uDAAuD;oBACvD,IAAI,EAAE,OAAO,IAAI,CAAA,GAAA,YAAI,MACnB,uBAAA,MAAM,aAAa,cAAnB,2CAAA,qBAAqB,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,WAAW;gBAEjD,OAAO,IAAI,EAAE,GAAG,KAAK,QACnB,MAAM,aAAa,GAAG,IAAI;YAE9B;YACA,SAAQ,CAAC;gBACP,IAAI,KAAK,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAClE;gBAGF,IAAI,KAAK,EAAE,MAAM,KAAK,KAAK,CAAC,MAAM,iBAAiB,IAAI,CAAC,AAAC,CAAA,GAAA,eAAO,EAAU,SAAS,EAAE;oBACnF,IAAI,wBAAwB;oBAC5B,IAAI,YACF,EAAE,cAAc;oBAGlB,iEAAiE;oBACjE,0CAA0C;oBAC1C,IAAI,CAAC,MAAM,yBAAyB,IAAI,CAAC,MAAM,SAAS,IAAK,CAAA,MAAM,WAAW,KAAK,aAAa,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,CAAA,GAAI;wBAC9H,IAAI,iBAAiB,kBAAkB,GAAG;wBAC1C,IAAI,cAAc,eAAe,GAAG;wBACpC,IAAI,eAAe,gBAAgB,GAAG;wBACtC,aAAa;wBACb,wBAAwB,kBAAkB,eAAe;oBAC3D,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM,WAAW,KAAK,YAAY;wBAC9D,IAAI,cAAc,MAAM,WAAW,IAAI,AAAC,EAAE,WAAW,CAAkB,WAAW,IAAmB;wBACrG,wBAAwB,gBAAgB,kCAAY,EAAE,aAAa,EAAE,IAAI,aAAa;wBACtF,MAAM,YAAY,GAAG;wBACrB,aAAa;wBACb,OAAO;oBACT;oBAEA,MAAM,yBAAyB,GAAG;oBAClC,IAAI,uBACF,EAAE,eAAe;gBAErB;YACF;QACF;QAEA,IAAI,UAAU,CAAC;gBA0BkB;YAzB/B,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI,2CAAqB,GAAG,MAAM,MAAM,GAAG;oBAwB5E;gBAvBA,IAAI,mDAA6B,CAAA,GAAA,qBAAa,EAAE,IAAI,EAAE,GAAG,GACvD,EAAE,cAAc;gBAGlB,IAAI,SAAS,CAAA,GAAA,qBAAa,EAAE;gBAC5B,IAAI,aAAa,CAAA,GAAA,mBAAW,EAAE,MAAM,MAAM,EAAE,CAAA,GAAA,qBAAa,EAAE;gBAC3D,gBAAgB,kCAAY,MAAM,MAAM,EAAE,IAAI,YAAY;gBAC1D,IAAI,YACF,sBAAsB,GAAG,MAAM,MAAM;gBAEvC;gBAEA,+EAA+E;gBAC/E,4EAA4E;gBAC5E,yCAAyC;gBACzC,IAAI,EAAE,GAAG,KAAK,WAAW,uCAAiB,MAAM,MAAM,KAAK,CAAA,GAAA,mBAAW,EAAE,MAAM,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,mCAAa,EAAE;oBACjH,2EAA2E;oBAC3E,yEAAyE;oBACzE,CAAC,CAAC,mCAAa,GAAG;oBAClB,CAAA,GAAA,eAAO,EAAE,MAAM,MAAM,EAAE,GAAG;gBAC5B;gBAEA,MAAM,SAAS,GAAG;iBAClB,wBAAA,MAAM,aAAa,cAAnB,4CAAA,sBAAqB,MAAM,CAAC,EAAE,GAAG;YACnC,OAAO,IAAI,EAAE,GAAG,KAAK,YAAU,uBAAA,MAAM,aAAa,cAAnB,2CAAA,qBAAqB,IAAI,GAAE;oBAOtD;gBANF,8EAA8E;gBAC9E,gFAAgF;gBAChF,oEAAoE;gBACpE,IAAI,SAAS,MAAM,aAAa;gBAChC,MAAM,aAAa,GAAG;gBACtB,KAAK,IAAI,SAAS,OAAO,MAAM,IAC7B,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,aAAa,CAAC,IAAI,cAAc,SAAS;YAE3D;QACF;QAEA,IAAI,OAAO,iBAAiB,aAAa;YACvC,WAAW,aAAa,GAAG,CAAC;gBAC1B,2EAA2E;gBAC3E,IAAI,EAAE,MAAM,KAAK,KAAK,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC/E;gBAGF,oFAAoF;gBACpF,8DAA8D;gBAC9D,iDAAiD;gBACjD,iDAAiD;gBACjD,IAAI,CAAA,GAAA,4BAAoB,EAAE,EAAE,WAAW,GAAG;oBACxC,MAAM,WAAW,GAAG;oBACpB;gBACF;gBAEA,MAAM,WAAW,GAAG,EAAE,WAAW;gBAEjC,IAAI,wBAAwB;gBAC5B,IAAI,CAAC,MAAM,SAAS,EAAE;oBACpB,MAAM,SAAS,GAAG;oBAClB,MAAM,YAAY,GAAG;oBACrB,MAAM,eAAe,GAAG,EAAE,SAAS;oBACnC,MAAM,MAAM,GAAG,EAAE,aAAa;oBAE9B,IAAI,CAAC,2BACH,CAAA,GAAA,yCAAmB,EAAE,MAAM,MAAM;oBAGnC,wBAAwB,kBAAkB,GAAG,MAAM,WAAW;oBAE9D,oFAAoF;oBACpF,0DAA0D;oBAC1D,IAAI,SAAS,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW;oBACzC,IAAI,2BAA2B,QAC7B,OAAO,qBAAqB,CAAC,EAAE,SAAS;oBAG1C,kBAAkB,CAAA,GAAA,uBAAe,EAAE,EAAE,aAAa,GAAG,aAAa,aAAa;oBAC/E,kBAAkB,CAAA,GAAA,uBAAe,EAAE,EAAE,aAAa,GAAG,iBAAiB,iBAAiB;gBACzF;gBAEA,IAAI,uBACF,EAAE,eAAe;YAErB;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,EAAE,MAAM,KAAK,GAAG;oBAClB,IAAI,qBAAqB;wBACvB,IAAI,UAAU,CAAA,GAAA,yCAAW,EAAE,EAAE,MAAM;wBACnC,IAAI,SACF,MAAM,WAAW,CAAC,IAAI,CAAC;oBAE3B;oBAEA,EAAE,eAAe;gBACnB;YACF;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,wGAAwG;gBACxG,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,MAAM,MAAM,WAAW,KAAK,WACzF;gBAGF,0BAA0B;gBAC1B,IAAI,EAAE,MAAM,KAAK,GACf,eAAe,GAAG,MAAM,WAAW,IAAI,EAAE,WAAW;YAExD;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,EAAE,SAAS,KAAK,MAAM,eAAe,IAAI,MAAM,MAAM,IAAI,CAAC,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,MAAM;oBAC7G,MAAM,YAAY,GAAG;oBACrB,kBAAkB,kCAAY,MAAM,MAAM,EAAE,IAAI,MAAM,WAAW;gBACnE;YACF;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,EAAE,SAAS,KAAK,MAAM,eAAe,IAAI,MAAM,MAAM,IAAI,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,MAAM;oBAC5G,MAAM,YAAY,GAAG;oBACrB,gBAAgB,kCAAY,MAAM,MAAM,EAAE,IAAI,MAAM,WAAW,EAAE;oBACjE,oBAAoB;gBACtB;YACF;YAEA,IAAI,cAAc,CAAC;gBACjB,IAAI,EAAE,SAAS,KAAK,MAAM,eAAe,IAAI,MAAM,SAAS,IAAI,EAAE,MAAM,KAAK,KAAK,MAAM,MAAM,EAAE;oBAC9F,IAAI,CAAA,GAAA,mBAAW,EAAE,MAAM,MAAM,EAAE,CAAA,GAAA,qBAAa,EAAE,OAAO,MAAM,WAAW,IAAI,MAAM;wBAC9E,4EAA4E;wBAC5E,iGAAiG;wBACjG,sDAAsD;wBACtD,8CAA8C;wBAC9C,4EAA4E;wBAC5E,uEAAuE;wBACvE,oFAAoF;wBACpF,qFAAqF;wBACrF,sEAAsE;wBACtE,8IAA8I;wBAC9I,IAAI,UAAU;wBACd,IAAI,UAAU,WAAW;4BACvB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,YAAY;gCAC7C,IAAI,SACF,OAAO;qCACF;oCACL,CAAA,GAAA,4BAAoB,EAAE,MAAM,MAAM;oCAClC,MAAM,MAAM,CAAC,KAAK;gCACpB;;wBAEJ,GAAG;wBACH,yDAAyD;wBACzD,+DAA+D;wBAC/D,kBAAkB,EAAE,aAAa,EAAc,SAAS,IAAM,UAAU,MAAM;wBAC9E,MAAM,WAAW,CAAC,IAAI,CAAC,IAAM,aAAa;oBAC5C,OACE,OAAO;oBAGT,0EAA0E;oBAC1E,MAAM,YAAY,GAAG;gBACvB;YACF;YAEA,IAAI,kBAAkB,CAAC;gBACrB,OAAO;YACT;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,0FAA0F;gBAC1F,OAAO;YACT;QACF,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAAQ;YAC1C,6DAA6D;YAC7D,qEAAqE;YAErE,WAAW,WAAW,GAAG,CAAC;gBACxB,0BAA0B;gBAC1B,IAAI,EAAE,MAAM,KAAK,KAAK,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC/E;gBAGF,IAAI,MAAM,yBAAyB,EAAE;oBACnC,EAAE,eAAe;oBACjB;gBACF;gBAEA,MAAM,SAAS,GAAG;gBAClB,MAAM,YAAY,GAAG;gBACrB,MAAM,MAAM,GAAG,EAAE,aAAa;gBAC9B,MAAM,WAAW,GAAG,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAAI,YAAY;gBAEhE,qGAAqG;gBACrG,IAAI,wBAAwB,CAAA,GAAA,gBAAQ,EAAE,IAAM,kBAAkB,GAAG,MAAM,WAAW;gBAClF,IAAI,uBACF,EAAE,eAAe;gBAGnB,IAAI,qBAAqB;oBACvB,IAAI,UAAU,CAAA,GAAA,yCAAW,EAAE,EAAE,MAAM;oBACnC,IAAI,SACF,MAAM,WAAW,CAAC,IAAI,CAAC;gBAE3B;gBAEA,kBAAkB,CAAA,GAAA,uBAAe,EAAE,EAAE,aAAa,GAAG,WAAW,WAAW;YAC7E;YAEA,WAAW,YAAY,GAAG,CAAC;gBACzB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,wBAAwB;gBAC5B,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,yBAAyB,IAAI,MAAM,WAAW,IAAI,MAAM;oBACpF,MAAM,YAAY,GAAG;oBACrB,wBAAwB,kBAAkB,GAAG,MAAM,WAAW;gBAChE;gBAEA,IAAI,uBACF,EAAE,eAAe;YAErB;YAEA,WAAW,YAAY,GAAG,CAAC;gBACzB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,wBAAwB;gBAC5B,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,yBAAyB,IAAI,MAAM,WAAW,IAAI,MAAM;oBACpF,MAAM,YAAY,GAAG;oBACrB,wBAAwB,gBAAgB,GAAG,MAAM,WAAW,EAAE;oBAC9D,oBAAoB;gBACtB;gBAEA,IAAI,uBACF,EAAE,eAAe;YAErB;YAEA,WAAW,SAAS,GAAG,CAAC;gBACtB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,CAAC,MAAM,yBAAyB,IAAI,EAAE,MAAM,KAAK,GACnD,eAAe,GAAG,MAAM,WAAW,IAAI;YAE3C;YAEA,IAAI,YAAY,CAAC;gBACf,0BAA0B;gBAC1B,IAAI,EAAE,MAAM,KAAK,GACf;gBAGF,IAAI,MAAM,yBAAyB,EAAE;oBACnC,MAAM,yBAAyB,GAAG;oBAClC;gBACF;gBAEA,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,KAAgB,MAAM,WAAW,IAAI;qBAIrF,OAAO;gBAGT,MAAM,YAAY,GAAG;YACvB;YAEA,WAAW,YAAY,GAAG,CAAC;gBACzB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,QAAQ,wCAAkB,EAAE,WAAW;gBAC3C,IAAI,CAAC,OACH;gBAEF,MAAM,eAAe,GAAG,MAAM,UAAU;gBACxC,MAAM,yBAAyB,GAAG;gBAClC,MAAM,YAAY,GAAG;gBACrB,MAAM,SAAS,GAAG;gBAClB,MAAM,MAAM,GAAG,EAAE,aAAa;gBAC9B,MAAM,WAAW,GAAG;gBAEpB,IAAI,CAAC,2BACH,CAAA,GAAA,yCAAmB,EAAE,MAAM,MAAM;gBAGnC,IAAI,wBAAwB,kBAAkB,uCAAiB,MAAM,MAAM,EAAE,IAAI,MAAM,WAAW;gBAClG,IAAI,uBACF,EAAE,eAAe;gBAGnB,kBAAkB,CAAA,GAAA,qBAAa,EAAE,EAAE,aAAa,GAAG,UAAU,UAAU;YACzE;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,CAAC,MAAM,SAAS,EAAE;oBACpB,EAAE,eAAe;oBACjB;gBACF;gBAEA,IAAI,QAAQ,mCAAa,EAAE,WAAW,EAAE,MAAM,eAAe;gBAC7D,IAAI,wBAAwB;gBAC5B,IAAI,SAAS,mCAAa,OAAO,EAAE,aAAa,GAC9C;oBAAA,IAAI,CAAC,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,MAAM;wBACpD,MAAM,YAAY,GAAG;wBACrB,wBAAwB,kBAAkB,uCAAiB,MAAM,MAAM,EAAG,IAAI,MAAM,WAAW;oBACjG;gBAAA,OACK,IAAI,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,MAAM;oBAC1D,MAAM,YAAY,GAAG;oBACrB,wBAAwB,gBAAgB,uCAAiB,MAAM,MAAM,EAAG,IAAI,MAAM,WAAW,EAAE;oBAC/F,oBAAoB,uCAAiB,MAAM,MAAM,EAAG;gBACtD;gBAEA,IAAI,uBACF,EAAE,eAAe;YAErB;YAEA,WAAW,UAAU,GAAG,CAAC;gBACvB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,CAAC,MAAM,SAAS,EAAE;oBACpB,EAAE,eAAe;oBACjB;gBACF;gBAEA,IAAI,QAAQ,mCAAa,EAAE,WAAW,EAAE,MAAM,eAAe;gBAC7D,IAAI,wBAAwB;gBAC5B,IAAI,SAAS,mCAAa,OAAO,EAAE,aAAa,KAAK,MAAM,WAAW,IAAI,MAAM;oBAC9E,eAAe,uCAAiB,MAAM,MAAM,EAAG,IAAI,MAAM,WAAW;oBACpE,wBAAwB,gBAAgB,uCAAiB,MAAM,MAAM,EAAG,IAAI,MAAM,WAAW;oBAC7F,sBAAsB,EAAE,WAAW,EAAE,MAAM,MAAM;gBACnD,OAAO,IAAI,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,MACpD,wBAAwB,gBAAgB,uCAAiB,MAAM,MAAM,EAAG,IAAI,MAAM,WAAW,EAAE;gBAGjG,IAAI,uBACF,EAAE,eAAe;gBAGnB,MAAM,SAAS,GAAG;gBAClB,MAAM,eAAe,GAAG;gBACxB,MAAM,YAAY,GAAG;gBACrB,MAAM,yBAAyB,GAAG;gBAClC,IAAI,MAAM,MAAM,IAAI,CAAC,2BACnB,CAAA,GAAA,yCAAmB,EAAE,MAAM,MAAM;gBAEnC;YACF;YAEA,WAAW,aAAa,GAAG,CAAC;gBAC1B,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,EAAE,eAAe;gBACjB,IAAI,MAAM,SAAS,EACjB,OAAO,uCAAiB,MAAM,MAAM,EAAG;YAE3C;YAEA,IAAI,WAAW,CAAC;gBACd,IAAI,MAAM,SAAS,IAAI,CAAA,GAAA,mBAAW,EAAE,CAAA,GAAA,qBAAa,EAAE,IAAI,MAAM,MAAM,GACjE,OAAO;oBACL,eAAe,MAAM,MAAM;oBAC3B,UAAU;oBACV,SAAS;oBACT,SAAS;oBACT,QAAQ;gBACV;YAEJ;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,EAAE,aAAa,EAAE,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,OAAO;YACT;QACF;QAEA,OAAO;IACT,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,yDAAyD;IACzD,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,CAAC,UAAU,QAAQ,GAAG,CAAC,QAAQ,KAAK,QACtC;QAGF,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,OAAO,OAAO;QACrD,IAAI,CAAC,iBAAiB,CAAC,cAAc,IAAI,IAAI,cAAc,cAAc,CAAC,iCACxE;QAGF,MAAM,QAAQ,cAAc,aAAa,CAAC;QAC1C,MAAM,EAAE,GAAG;QACX,mEAAmE;QACnE,0DAA0D;QAC1D,iDAAiD;QACjD,MAAM,WAAW,GAAG,CAAC;;GAEtB,EAAE,0CAAoB;;;;IAIrB,CAAC,CAAC,IAAI;QACN,cAAc,IAAI,CAAC,OAAO,CAAC;IAC7B,GAAG;QAAC;KAAO;IAEX,mFAAmF;IACnF,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,QAAQ,IAAI,OAAO;QACvB,OAAO;gBAEkB;YADvB,IAAI,CAAC,2BACH,CAAA,GAAA,yCAAmB,EAAE,CAAA,gBAAA,MAAM,MAAM,cAAZ,2BAAA,gBAAgB;YAEvC,KAAK,IAAI,WAAW,MAAM,WAAW,CACnC;YAEF,MAAM,WAAW,GAAG,EAAE;QACxB;IACF,GAAG;QAAC;KAA0B;IAE9B,OAAO;QACL,WAAW,iBAAiB;QAC5B,YAAY,CAAA,GAAA,iBAAS,EAAE,UAAU,YAAY;YAAC,CAAC,0CAAoB,EAAE;QAAI;IAC3E;AACF;AAEA,SAAS,uCAAiB,MAAe;IACvC,OAAO,OAAO,OAAO,KAAK,OAAO,OAAO,YAAY,CAAC;AACvD;AAEA,SAAS,2CAAqB,KAAoB,EAAE,aAAsB;IACxE,MAAM,OAAC,GAAG,QAAE,IAAI,EAAC,GAAG;IACpB,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,YAAY,CAAC;IAClC,qDAAqD;IACrD,0BAA0B;IAC1B,OACE,AAAC,CAAA,QAAQ,WAAW,QAAQ,OAAO,QAAQ,cAAc,SAAS,OAAM,KACxE,CAAE,CAAA,AAAC,mBAAmB,CAAA,GAAA,qBAAa,EAAE,SAAS,gBAAgB,IAAI,CAAC,sCAAgB,SAAS,QAC1F,mBAAmB,CAAA,GAAA,qBAAa,EAAE,SAAS,mBAAmB,IAC9D,QAAQ,iBAAiB,AAAD,KAC1B,2CAA2C;IAC3C,CAAE,CAAA,AAAC,CAAA,SAAS,UAAW,CAAC,QAAQ,uCAAiB,QAAQ,KAAM,QAAQ,OAAM;AAEjF;AAEA,SAAS,wCAAkB,KAAiB;IAC1C,MAAM,iBAAC,aAAa,EAAC,GAAG;IACxB,IAAI,cAAc,MAAM,GAAG,GACzB,OAAO,aAAa,CAAC,EAAE;IAEzB,OAAO;AACT;AAEA,SAAS,mCACP,KAAiB,EACjB,SAAwB;IAExB,MAAM,iBAAiB,MAAM,cAAc;IAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;QAC9C,MAAM,QAAQ,cAAc,CAAC,EAAE;QAC/B,IAAI,MAAM,UAAU,KAAK,WACvB,OAAO;IAEX;IACA,OAAO;AACT;AAEA,SAAS,uCAAiB,MAAwB,EAAE,CAAgC;IAClF,IAAI,UAAU;IACd,IAAI,UAAU;IACd,IAAI,EAAE,aAAa,IAAI,EAAE,aAAa,CAAC,MAAM,KAAK,GAAG;QACnD,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO;QACpC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO;IACtC;IACA,OAAO;QACL,eAAe;QACf,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,OAAO;QAClB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,MAAM;iBAChB;iBACA;IACF;AACF;AAEA,SAAS,kCAAY,MAAwB,EAAE,CAAY;IACzD,IAAI,UAAU,EAAE,OAAO;IACvB,IAAI,UAAU,EAAE,OAAO;IACvB,OAAO;QACL,eAAe;QACf,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,OAAO;QAClB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,MAAM;iBAChB;iBACA;IACF;AACF;AAkBA,SAAS,yCAAmB,KAAiB;IAC3C,IAAI,UAAU;IACd,IAAI,UAAU;IACd,IAAI,MAAM,KAAK,KAAK,WAClB,UAAW,MAAM,KAAK,GAAG;SACpB,IAAI,MAAM,OAAO,KAAK,WAC3B,UAAU,MAAM,OAAO;IAEzB,IAAI,MAAM,MAAM,KAAK,WACnB,UAAW,MAAM,MAAM,GAAG;SACrB,IAAI,MAAM,OAAO,KAAK,WAC3B,UAAU,MAAM,OAAO;IAGzB,OAAO;QACL,KAAK,MAAM,OAAO,GAAG;QACrB,OAAO,MAAM,OAAO,GAAG;QACvB,QAAQ,MAAM,OAAO,GAAG;QACxB,MAAM,MAAM,OAAO,GAAG;IACxB;AACF;AAEA,SAAS,+CAAyB,CAAO,EAAE,CAAO;IAChD,yCAAyC;IACzC,IAAI,EAAE,IAAI,GAAG,EAAE,KAAK,IAAI,EAAE,IAAI,GAAG,EAAE,KAAK,EACtC,OAAO;IAET,yCAAyC;IACzC,IAAI,EAAE,GAAG,GAAG,EAAE,MAAM,IAAI,EAAE,GAAG,GAAG,EAAE,MAAM,EACtC,OAAO;IAET,OAAO;AACT;AAEA,SAAS,mCAAa,KAAiB,EAAE,MAAe;IACtD,IAAI,OAAO,OAAO,qBAAqB;IACvC,IAAI,YAAY,yCAAmB;IACnC,OAAO,+CAAyB,MAAM;AACxC;AAEA,SAAS,6CAAuB,MAAe;IAC7C,IAAI,kBAAkB,kBACpB,OAAO;IAGT,IAAI,kBAAkB,mBACpB,OAAO,OAAO,IAAI,KAAK,YAAY,OAAO,IAAI,KAAK;IAGrD,IAAI,uCAAiB,SACnB,OAAO;IAGT,OAAO;AACT;AAEA,SAAS,mDAA6B,MAAe,EAAE,GAAW;IAChE,IAAI,kBAAkB,kBACpB,OAAO,CAAC,sCAAgB,QAAQ;IAGlC,OAAO,6CAAuB;AAChC;AAEA,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,sCAAgB,MAAwB,EAAE,GAAW;IAC5D,6DAA6D;IAC7D,OAAO,OAAO,IAAI,KAAK,cAAc,OAAO,IAAI,KAAK,UACjD,QAAQ,MACR,wCAAkB,GAAG,CAAC,OAAO,IAAI;AACvC", "sources": ["packages/@react-aria/interactions/src/usePress.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {\n  chain,\n  focusWithoutScrolling,\n  getEventTarget,\n  getOwnerDocument,\n  getOwnerWindow,\n  isMac,\n  isVirtualClick,\n  isVirtualPointerEvent,\n  mergeProps,\n  nodeContains,\n  openLink,\n  useEffectEvent,\n  useGlobalListeners,\n  useSyncRef\n} from '@react-aria/utils';\nimport {createSyntheticEvent, preventFocus, setEventTarget} from './utils';\nimport {disableTextSelection, restoreTextSelection} from './textSelection';\nimport {DOMAttributes, FocusableElement, PressEvent as IPressEvent, PointerType, PressEvents, RefObject} from '@react-types/shared';\nimport {flushSync} from 'react-dom';\nimport {PressResponderContext} from './context';\nimport {MouseEvent as RMouseEvent, TouchEvent as RTouchEvent, useContext, useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface PressProps extends PressEvents {\n  /** Whether the target is in a controlled press state (e.g. an overlay it triggers is open). */\n  isPressed?: boolean,\n  /** Whether the press events should be disabled. */\n  isDisabled?: boolean,\n  /** Whether the target should not receive focus on press. */\n  preventFocusOnPress?: boolean,\n  /**\n   * Whether press events should be canceled when the pointer leaves the target while pressed.\n   * By default, this is `false`, which means if the pointer returns back over the target while\n   * still pressed, onPressStart will be fired again. If set to `true`, the press is canceled\n   * when the pointer leaves the target and onPressStart will not be fired if the pointer returns.\n   */\n  shouldCancelOnPointerExit?: boolean,\n  /** Whether text selection should be enabled on the pressable element. */\n  allowTextSelectionOnPress?: boolean\n}\n\nexport interface PressHookProps extends PressProps {\n  /** A ref to the target element. */\n  ref?: RefObject<Element | null>\n}\n\ninterface PressState {\n  isPressed: boolean,\n  ignoreEmulatedMouseEvents: boolean,\n  didFirePressStart: boolean,\n  isTriggeringEvent: boolean,\n  activePointerId: any,\n  target: FocusableElement | null,\n  isOverTarget: boolean,\n  pointerType: PointerType | null,\n  userSelect?: string,\n  metaKeyEvents?: Map<string, KeyboardEvent>,\n  disposables: Array<() => void>\n}\n\ninterface EventBase {\n  currentTarget: EventTarget | null,\n  shiftKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean,\n  altKey: boolean,\n  clientX?: number,\n  clientY?: number,\n  targetTouches?: Array<{clientX?: number, clientY?: number}>\n}\n\nexport interface PressResult {\n  /** Whether the target is currently pressed. */\n  isPressed: boolean,\n  /** Props to spread on the target element. */\n  pressProps: DOMAttributes\n}\n\nfunction usePressResponderContext(props: PressHookProps): PressHookProps {\n  // Consume context from <PressResponder> and merge with props.\n  let context = useContext(PressResponderContext);\n  if (context) {\n    let {register, ...contextProps} = context;\n    props = mergeProps(contextProps, props) as PressHookProps;\n    register();\n  }\n  useSyncRef(context, props.ref);\n\n  return props;\n}\n\nclass PressEvent implements IPressEvent {\n  type: IPressEvent['type'];\n  pointerType: PointerType;\n  target: Element;\n  shiftKey: boolean;\n  ctrlKey: boolean;\n  metaKey: boolean;\n  altKey: boolean;\n  x: number;\n  y: number;\n  #shouldStopPropagation = true;\n\n  constructor(type: IPressEvent['type'], pointerType: PointerType, originalEvent: EventBase, state?: PressState) {\n    let currentTarget = state?.target ?? originalEvent.currentTarget;\n    const rect: DOMRect | undefined = (currentTarget as Element)?.getBoundingClientRect();\n    let x, y = 0;\n    let clientX, clientY: number | null = null;\n    if (originalEvent.clientX != null && originalEvent.clientY != null) {\n      clientX = originalEvent.clientX;\n      clientY = originalEvent.clientY;\n    }\n    if (rect) {\n      if (clientX != null && clientY != null) {\n        x = clientX - rect.left;\n        y = clientY - rect.top;\n      } else {\n        x = rect.width / 2;\n        y = rect.height / 2;\n      }\n    }\n    this.type = type;\n    this.pointerType = pointerType;\n    this.target = originalEvent.currentTarget as Element;\n    this.shiftKey = originalEvent.shiftKey;\n    this.metaKey = originalEvent.metaKey;\n    this.ctrlKey = originalEvent.ctrlKey;\n    this.altKey = originalEvent.altKey;\n    this.x = x;\n    this.y = y;\n  }\n\n  continuePropagation() {\n    this.#shouldStopPropagation = false;\n  }\n\n  get shouldStopPropagation() {\n    return this.#shouldStopPropagation;\n  }\n}\n\nconst LINK_CLICKED = Symbol('linkClicked');\nconst STYLE_ID = 'react-aria-pressable-style';\nconst PRESSABLE_ATTRIBUTE = 'data-react-aria-pressable';\n\n/**\n * Handles press interactions across mouse, touch, keyboard, and screen readers.\n * It normalizes behavior across browsers and platforms, and handles many nuances\n * of dealing with pointer and keyboard events.\n */\nexport function usePress(props: PressHookProps): PressResult {\n  let {\n    onPress,\n    onPressChange,\n    onPressStart,\n    onPressEnd,\n    onPressUp,\n    onClick,\n    isDisabled,\n    isPressed: isPressedProp,\n    preventFocusOnPress,\n    shouldCancelOnPointerExit,\n    allowTextSelectionOnPress,\n    ref: domRef,\n    ...domProps\n  } = usePressResponderContext(props);\n\n  let [isPressed, setPressed] = useState(false);\n  let ref = useRef<PressState>({\n    isPressed: false,\n    ignoreEmulatedMouseEvents: false,\n    didFirePressStart: false,\n    isTriggeringEvent: false,\n    activePointerId: null,\n    target: null,\n    isOverTarget: false,\n    pointerType: null,\n    disposables: []\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let triggerPressStart = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled || state.didFirePressStart) {\n      return false;\n    }\n\n    let shouldStopPropagation = true;\n    state.isTriggeringEvent = true;\n    if (onPressStart) {\n      let event = new PressEvent('pressstart', pointerType, originalEvent);\n      onPressStart(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(true);\n    }\n\n    state.isTriggeringEvent = false;\n    state.didFirePressStart = true;\n    setPressed(true);\n    return shouldStopPropagation;\n  });\n\n  let triggerPressEnd = useEffectEvent((originalEvent: EventBase, pointerType: PointerType, wasPressed = true) => {\n    let state = ref.current;\n    if (!state.didFirePressStart) {\n      return false;\n    }\n\n    state.didFirePressStart = false;\n    state.isTriggeringEvent = true;\n\n    let shouldStopPropagation = true;\n    if (onPressEnd) {\n      let event = new PressEvent('pressend', pointerType, originalEvent);\n      onPressEnd(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(false);\n    }\n\n    setPressed(false);\n\n    if (onPress && wasPressed && !isDisabled) {\n      let event = new PressEvent('press', pointerType, originalEvent);\n      onPress(event);\n      shouldStopPropagation &&= event.shouldStopPropagation;\n    }\n\n    state.isTriggeringEvent = false;\n    return shouldStopPropagation;\n  });\n\n  let triggerPressUp = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled) {\n      return false;\n    }\n\n    if (onPressUp) {\n      state.isTriggeringEvent = true;\n      let event = new PressEvent('pressup', pointerType, originalEvent);\n      onPressUp(event);\n      state.isTriggeringEvent = false;\n      return event.shouldStopPropagation;\n    }\n\n    return true;\n  });\n\n  let cancel = useEffectEvent((e: EventBase) => {\n    let state = ref.current;\n    if (state.isPressed && state.target) {\n      if (state.didFirePressStart && state.pointerType != null) {\n        triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n      }\n      state.isPressed = false;\n      state.isOverTarget = false;\n      state.activePointerId = null;\n      state.pointerType = null;\n      removeAllGlobalListeners();\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    }\n  });\n\n  let cancelOnPointerExit = useEffectEvent((e: EventBase) => {\n    if (shouldCancelOnPointerExit) {\n      cancel(e);\n    }\n  });\n\n  let triggerClick = useEffectEvent((e: RMouseEvent<FocusableElement>) => {\n    onClick?.(e);\n  });\n\n  let triggerSyntheticClick = useEffectEvent((e: KeyboardEvent | TouchEvent, target: FocusableElement) => {\n    // Some third-party libraries pass in onClick instead of onPress.\n    // Create a fake mouse event and trigger onClick as well.\n    // This matches the browser's native activation behavior for certain elements (e.g. button).\n    // https://html.spec.whatwg.org/#activation\n    // https://html.spec.whatwg.org/#fire-a-synthetic-pointer-event\n    if (onClick) {\n      let event = new MouseEvent('click', e);\n      setEventTarget(event, target);\n      onClick(createSyntheticEvent(event));\n    }\n  });\n\n  let pressProps = useMemo(() => {\n    let state = ref.current;\n    let pressProps: DOMAttributes = {\n      onKeyDown(e) {\n        if (isValidKeyboardEvent(e.nativeEvent, e.currentTarget) && nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          if (shouldPreventDefaultKeyboard(getEventTarget(e.nativeEvent), e.key)) {\n            e.preventDefault();\n          }\n\n          // If the event is repeating, it may have started on a different element\n          // after which focus moved to the current element. Ignore these events and\n          // only handle the first key down event.\n          let shouldStopPropagation = true;\n          if (!state.isPressed && !e.repeat) {\n            state.target = e.currentTarget;\n            state.isPressed = true;\n            state.pointerType = 'keyboard';\n            shouldStopPropagation = triggerPressStart(e, 'keyboard');\n\n            // Focus may move before the key up event, so register the event on the document\n            // instead of the same element where the key down event occurred. Make it capturing so that it will trigger\n            // before stopPropagation from useKeyboard on a child element may happen and thus we can still call triggerPress for the parent element.\n            let originalTarget = e.currentTarget;\n            let pressUp = (e) => {\n              if (isValidKeyboardEvent(e, originalTarget) && !e.repeat && nodeContains(originalTarget, getEventTarget(e)) && state.target) {\n                triggerPressUp(createEvent(state.target, e), 'keyboard');\n              }\n            };\n\n            addGlobalListener(getOwnerDocument(e.currentTarget), 'keyup', chain(pressUp, onKeyUp), true);\n          }\n\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n\n          // Keep track of the keydown events that occur while the Meta (e.g. Command) key is held.\n          // macOS has a bug where keyup events are not fired while the Meta key is down.\n          // When the Meta key itself is released we will get an event for that, and we'll act as if\n          // all of these other keys were released as well.\n          // https://bugs.chromium.org/p/chromium/issues/detail?id=1393524\n          // https://bugs.webkit.org/show_bug.cgi?id=55291\n          // https://bugzilla.mozilla.org/show_bug.cgi?id=1299553\n          if (e.metaKey && isMac()) {\n            state.metaKeyEvents?.set(e.key, e.nativeEvent);\n          }\n        } else if (e.key === 'Meta') {\n          state.metaKeyEvents = new Map();\n        }\n      },\n      onClick(e) {\n        if (e && !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e && e.button === 0 && !state.isTriggeringEvent && !(openLink as any).isOpening) {\n          let shouldStopPropagation = true;\n          if (isDisabled) {\n            e.preventDefault();\n          }\n          \n          // If triggered from a screen reader or by using element.click(),\n          // trigger as if it were a keyboard click.\n          if (!state.ignoreEmulatedMouseEvents && !state.isPressed && (state.pointerType === 'virtual' || isVirtualClick(e.nativeEvent))) {\n            let stopPressStart = triggerPressStart(e, 'virtual');\n            let stopPressUp = triggerPressUp(e, 'virtual');\n            let stopPressEnd = triggerPressEnd(e, 'virtual');\n            triggerClick(e);\n            shouldStopPropagation = stopPressStart && stopPressUp && stopPressEnd;\n          } else if (state.isPressed && state.pointerType !== 'keyboard') {\n            let pointerType = state.pointerType || (e.nativeEvent as PointerEvent).pointerType as PointerType || 'virtual';\n            shouldStopPropagation = triggerPressEnd(createEvent(e.currentTarget, e), pointerType, true);\n            state.isOverTarget = false;\n            triggerClick(e);\n            cancel(e);\n          }\n\n          state.ignoreEmulatedMouseEvents = false;\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n        }\n      }\n    };\n\n    let onKeyUp = (e: KeyboardEvent) => {\n      if (state.isPressed && state.target && isValidKeyboardEvent(e, state.target)) {\n        if (shouldPreventDefaultKeyboard(getEventTarget(e), e.key)) {\n          e.preventDefault();\n        }\n\n        let target = getEventTarget(e);\n        let wasPressed = nodeContains(state.target, getEventTarget(e));\n        triggerPressEnd(createEvent(state.target, e), 'keyboard', wasPressed);\n        if (wasPressed) {\n          triggerSyntheticClick(e, state.target);\n        }\n        removeAllGlobalListeners();\n\n        // If a link was triggered with a key other than Enter, open the URL ourselves.\n        // This means the link has a role override, and the default browser behavior\n        // only applies when using the Enter key.\n        if (e.key !== 'Enter' && isHTMLAnchorLink(state.target) && nodeContains(state.target, target) && !e[LINK_CLICKED]) {\n          // Store a hidden property on the event so we only trigger link click once,\n          // even if there are multiple usePress instances attached to the element.\n          e[LINK_CLICKED] = true;\n          openLink(state.target, e, false);\n        }\n\n        state.isPressed = false;\n        state.metaKeyEvents?.delete(e.key);\n      } else if (e.key === 'Meta' && state.metaKeyEvents?.size) {\n        // If we recorded keydown events that occurred while the Meta key was pressed,\n        // and those haven't received keyup events already, fire keyup events ourselves.\n        // See comment above for more info about the macOS bug causing this.\n        let events = state.metaKeyEvents;\n        state.metaKeyEvents = undefined;\n        for (let event of events.values()) {\n          state.target?.dispatchEvent(new KeyboardEvent('keyup', event));\n        }\n      }\n    };\n\n    if (typeof PointerEvent !== 'undefined') {\n      pressProps.onPointerDown = (e) => {\n        // Only handle left clicks, and ignore events that bubbled through portals.\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // iOS safari fires pointer events from VoiceOver with incorrect coordinates/target.\n        // Ignore and let the onClick handler take care of it instead.\n        // https://bugs.webkit.org/show_bug.cgi?id=222627\n        // https://bugs.webkit.org/show_bug.cgi?id=223202\n        if (isVirtualPointerEvent(e.nativeEvent)) {\n          state.pointerType = 'virtual';\n          return;\n        }\n\n        state.pointerType = e.pointerType;\n\n        let shouldStopPropagation = true;\n        if (!state.isPressed) {\n          state.isPressed = true;\n          state.isOverTarget = true;\n          state.activePointerId = e.pointerId;\n          state.target = e.currentTarget as FocusableElement;\n\n          if (!allowTextSelectionOnPress) {\n            disableTextSelection(state.target);\n          }\n\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n\n          // Release pointer capture so that touch interactions can leave the original target.\n          // This enables onPointerLeave and onPointerEnter to fire.\n          let target = getEventTarget(e.nativeEvent);\n          if ('releasePointerCapture' in target) {\n            target.releasePointerCapture(e.pointerId);\n          }\n\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointerup', onPointerUp, false);\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointercancel', onPointerCancel, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseDown = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e.button === 0) {\n          if (preventFocusOnPress) {\n            let dispose = preventFocus(e.target as FocusableElement);\n            if (dispose) {\n              state.disposables.push(dispose);\n            }\n          }\n\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onPointerUp = (e) => {\n        // iOS fires pointerup with zero width and height, so check the pointerType recorded during pointerdown.\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent)) || state.pointerType === 'virtual') {\n          return;\n        }\n\n        // Only handle left clicks\n        if (e.button === 0) {\n          triggerPressUp(e, state.pointerType || e.pointerType);\n        }\n      };\n\n      pressProps.onPointerEnter = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && !state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = true;\n          triggerPressStart(createEvent(state.target, e), state.pointerType);\n        }\n      };\n\n      pressProps.onPointerLeave = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n      };\n\n      let onPointerUp = (e: PointerEvent) => {\n        if (e.pointerId === state.activePointerId && state.isPressed && e.button === 0 && state.target) {\n          if (nodeContains(state.target, getEventTarget(e)) && state.pointerType != null) {\n            // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n            // is mutated between onPointerUp and onClick, and is more compatible with third party libraries.\n            // https://github.com/adobe/react-spectrum/issues/1513\n            // https://issues.chromium.org/issues/40732224\n            // However, iOS and Android do not focus or fire onClick after a long press.\n            // We work around this by triggering a click ourselves after a timeout.\n            // This timeout is canceled during the click event in case the real one fires first.\n            // The timeout must be at least 32ms, because Safari on iOS delays the click event on\n            // non-form elements without certain ARIA roles (for hover emulation).\n            // https://github.com/WebKit/WebKit/blob/dccfae42bb29bd4bdef052e469f604a9387241c0/Source/WebKit/WebProcess/WebPage/ios/WebPageIOS.mm#L875-L892\n            let clicked = false;\n            let timeout = setTimeout(() => {\n              if (state.isPressed && state.target instanceof HTMLElement) {\n                if (clicked) {\n                  cancel(e);\n                } else {\n                  focusWithoutScrolling(state.target);\n                  state.target.click();\n                }\n              }\n            }, 80);\n            // Use a capturing listener to track if a click occurred.\n            // If stopPropagation is called it may never reach our handler.\n            addGlobalListener(e.currentTarget as Document, 'click', () => clicked = true, true);\n            state.disposables.push(() => clearTimeout(timeout));\n          } else {\n            cancel(e);\n          }\n\n          // Ignore subsequent onPointerLeave event before onClick on touch devices.\n          state.isOverTarget = false;\n        }\n      };\n\n      let onPointerCancel = (e: PointerEvent) => {\n        cancel(e);\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // Safari does not call onPointerCancel when a drag starts, whereas Chrome and Firefox do.\n        cancel(e);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      // NOTE: this fallback branch is entirely used by unit tests.\n      // All browsers now support pointer events, but JSDOM still does not.\n\n      pressProps.onMouseDown = (e) => {\n        // Only handle left clicks\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          e.stopPropagation();\n          return;\n        }\n\n        state.isPressed = true;\n        state.isOverTarget = true;\n        state.target = e.currentTarget;\n        state.pointerType = isVirtualClick(e.nativeEvent) ? 'virtual' : 'mouse';\n\n        // Flush sync so that focus moved during react re-renders occurs before we yield back to the browser.\n        let shouldStopPropagation = flushSync(() => triggerPressStart(e, state.pointerType!));\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        if (preventFocusOnPress) {\n          let dispose = preventFocus(e.target as FocusableElement);\n          if (dispose) {\n            state.disposables.push(dispose);\n          }\n        }\n\n        addGlobalListener(getOwnerDocument(e.currentTarget), 'mouseup', onMouseUp, false);\n      };\n\n      pressProps.onMouseEnter = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = true;\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseLeave = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(e, state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseUp = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.ignoreEmulatedMouseEvents && e.button === 0) {\n          triggerPressUp(e, state.pointerType || 'mouse');\n        }\n      };\n\n      let onMouseUp = (e: MouseEvent) => {\n        // Only handle left clicks\n        if (e.button !== 0) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n          return;\n        }\n\n        if (state.target && state.target.contains(e.target as Element) && state.pointerType != null) {\n          // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n          // is mutated between onMouseUp and onClick, and is more compatible with third party libraries.\n        } else {\n          cancel(e);\n        }\n\n        state.isOverTarget = false;\n      };\n\n      pressProps.onTouchStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let touch = getTouchFromEvent(e.nativeEvent);\n        if (!touch) {\n          return;\n        }\n        state.activePointerId = touch.identifier;\n        state.ignoreEmulatedMouseEvents = true;\n        state.isOverTarget = true;\n        state.isPressed = true;\n        state.target = e.currentTarget;\n        state.pointerType = 'touch';\n\n        if (!allowTextSelectionOnPress) {\n          disableTextSelection(state.target);\n        }\n\n        let shouldStopPropagation = triggerPressStart(createTouchEvent(state.target, e), state.pointerType);\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        addGlobalListener(getOwnerWindow(e.currentTarget), 'scroll', onScroll, true);\n      };\n\n      pressProps.onTouchMove = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget)) {\n          if (!state.isOverTarget && state.pointerType != null) {\n            state.isOverTarget = true;\n            shouldStopPropagation = triggerPressStart(createTouchEvent(state.target!, e), state.pointerType);\n          }\n        } else if (state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n          cancelOnPointerExit(createTouchEvent(state.target!, e));\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onTouchEnd = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget) && state.pointerType != null) {\n          triggerPressUp(createTouchEvent(state.target!, e), state.pointerType);\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType);\n          triggerSyntheticClick(e.nativeEvent, state.target!);\n        } else if (state.isOverTarget && state.pointerType != null) {\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        state.isPressed = false;\n        state.activePointerId = null;\n        state.isOverTarget = false;\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.target && !allowTextSelectionOnPress) {\n          restoreTextSelection(state.target);\n        }\n        removeAllGlobalListeners();\n      };\n\n      pressProps.onTouchCancel = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        e.stopPropagation();\n        if (state.isPressed) {\n          cancel(createTouchEvent(state.target!, e));\n        }\n      };\n\n      let onScroll = (e: Event) => {\n        if (state.isPressed && nodeContains(getEventTarget(e), state.target)) {\n          cancel({\n            currentTarget: state.target,\n            shiftKey: false,\n            ctrlKey: false,\n            metaKey: false,\n            altKey: false\n          });\n        }\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        cancel(e);\n      };\n    }\n\n    return pressProps;\n  }, [\n    addGlobalListener,\n    isDisabled,\n    preventFocusOnPress,\n    removeAllGlobalListeners,\n    allowTextSelectionOnPress,\n    cancel,\n    cancelOnPointerExit,\n    triggerPressEnd,\n    triggerPressStart,\n    triggerPressUp,\n    triggerClick,\n    triggerSyntheticClick\n  ]);\n\n  // Avoid onClick delay for double tap to zoom by default.\n  useEffect(() => {\n    if (!domRef || process.env.NODE_ENV === 'test') {\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(domRef.current);\n    if (!ownerDocument || !ownerDocument.head || ownerDocument.getElementById(STYLE_ID)) {\n      return;\n    }\n\n    const style = ownerDocument.createElement('style');\n    style.id = STYLE_ID;\n    // touchAction: 'manipulation' is supposed to be equivalent, but in\n    // Safari it causes onPointerCancel not to fire on scroll.\n    // https://bugs.webkit.org/show_bug.cgi?id=240917\n    style.textContent = `\n@layer {\n  [${PRESSABLE_ATTRIBUTE}] {\n    touch-action: pan-x pan-y pinch-zoom;\n  }\n}\n    `.trim();\n    ownerDocument.head.prepend(style);\n  }, [domRef]);\n\n  // Remove user-select: none in case component unmounts immediately after pressStart\n  useEffect(() => {\n    let state = ref.current;\n    return () => {\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target ?? undefined);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    };\n  }, [allowTextSelectionOnPress]);\n\n  return {\n    isPressed: isPressedProp || isPressed,\n    pressProps: mergeProps(domProps, pressProps, {[PRESSABLE_ATTRIBUTE]: true})\n  };\n}\n\nfunction isHTMLAnchorLink(target: Element): target is HTMLAnchorElement {\n  return target.tagName === 'A' && target.hasAttribute('href');\n}\n\nfunction isValidKeyboardEvent(event: KeyboardEvent, currentTarget: Element): boolean {\n  const {key, code} = event;\n  const element = currentTarget as HTMLElement;\n  const role = element.getAttribute('role');\n  // Accessibility for keyboards. Space and Enter only.\n  // \"Spacebar\" is for IE 11\n  return (\n    (key === 'Enter' || key === ' ' || key === 'Spacebar' || code === 'Space') &&\n    !((element instanceof getOwnerWindow(element).HTMLInputElement && !isValidInputKey(element, key)) ||\n      element instanceof getOwnerWindow(element).HTMLTextAreaElement ||\n      element.isContentEditable) &&\n    // Links should only trigger with Enter key\n    !((role === 'link' || (!role && isHTMLAnchorLink(element))) && key !== 'Enter')\n  );\n}\n\nfunction getTouchFromEvent(event: TouchEvent): Touch | null {\n  const {targetTouches} = event;\n  if (targetTouches.length > 0) {\n    return targetTouches[0];\n  }\n  return null;\n}\n\nfunction getTouchById(\n  event: TouchEvent,\n  pointerId: null | number\n): null | Touch {\n  const changedTouches = event.changedTouches;\n  for (let i = 0; i < changedTouches.length; i++) {\n    const touch = changedTouches[i];\n    if (touch.identifier === pointerId) {\n      return touch;\n    }\n  }\n  return null;\n}\n\nfunction createTouchEvent(target: FocusableElement, e: RTouchEvent<FocusableElement>): EventBase {\n  let clientX = 0;\n  let clientY = 0;\n  if (e.targetTouches && e.targetTouches.length === 1) {\n    clientX = e.targetTouches[0].clientX;\n    clientY = e.targetTouches[0].clientY;\n  }\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\nfunction createEvent(target: FocusableElement, e: EventBase): EventBase {\n  let clientX = e.clientX;\n  let clientY = e.clientY;\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\ninterface Rect {\n  top: number,\n  right: number,\n  bottom: number,\n  left: number\n}\n\ninterface EventPoint {\n  clientX: number,\n  clientY: number,\n  width?: number,\n  height?: number,\n  radiusX?: number,\n  radiusY?: number\n}\n\nfunction getPointClientRect(point: EventPoint): Rect {\n  let offsetX = 0;\n  let offsetY = 0;\n  if (point.width !== undefined) {\n    offsetX = (point.width / 2);\n  } else if (point.radiusX !== undefined) {\n    offsetX = point.radiusX;\n  }\n  if (point.height !== undefined) {\n    offsetY = (point.height / 2);\n  } else if (point.radiusY !== undefined) {\n    offsetY = point.radiusY;\n  }\n\n  return {\n    top: point.clientY - offsetY,\n    right: point.clientX + offsetX,\n    bottom: point.clientY + offsetY,\n    left: point.clientX - offsetX\n  };\n}\n\nfunction areRectanglesOverlapping(a: Rect, b: Rect) {\n  // check if they cannot overlap on x axis\n  if (a.left > b.right || b.left > a.right) {\n    return false;\n  }\n  // check if they cannot overlap on y axis\n  if (a.top > b.bottom || b.top > a.bottom) {\n    return false;\n  }\n  return true;\n}\n\nfunction isOverTarget(point: EventPoint, target: Element) {\n  let rect = target.getBoundingClientRect();\n  let pointRect = getPointClientRect(point);\n  return areRectanglesOverlapping(rect, pointRect);\n}\n\nfunction shouldPreventDefaultUp(target: Element) {\n  if (target instanceof HTMLInputElement) {\n    return false;\n  }\n\n  if (target instanceof HTMLButtonElement) {\n    return target.type !== 'submit' && target.type !== 'reset';\n  }\n\n  if (isHTMLAnchorLink(target)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction shouldPreventDefaultKeyboard(target: Element, key: string) {\n  if (target instanceof HTMLInputElement) {\n    return !isValidInputKey(target, key);\n  }\n\n  return shouldPreventDefaultUp(target);\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\nfunction isValidInputKey(target: HTMLInputElement, key: string) {\n  // Only space should toggle checkboxes and radios, not enter.\n  return target.type === 'checkbox' || target.type === 'radio'\n    ? key === ' '\n    : nonTextInputTypes.has(target.type);\n}\n"], "names": [], "version": 3, "file": "usePress.module.js.map"}