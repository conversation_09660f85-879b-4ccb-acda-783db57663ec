var h=Object.defineProperty;var y=(e,i,t)=>i in e?h(e,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[i]=t;var g=(e,i,t)=>(y(e,typeof i!="symbol"?i+"":i,t),t);import{Machine as A,batch as v}from'../../machine.js';import{ActionTypes as M,stackMachines as T}from'../../machines/stack-machine.js';import{Focus as c,calculateActiveIndex as f}from'../../utils/calculate-active-index.js';import{sortByDomNode as R}from'../../utils/focus-management.js';import{match as b}from'../../utils/match.js';var E=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(E||{}),O=(t=>(t[t.Pointer=0]="Pointer",t[t.Other=1]="Other",t))(O||{}),F=(r=>(r[r.OpenMenu=0]="OpenMenu",r[r.CloseMenu=1]="CloseMenu",r[r.GoToItem=2]="GoToItem",r[r.Search=3]="Search",r[r.ClearSearch=4]="ClearSearch",r[r.RegisterItems=5]="RegisterItems",r[r.UnregisterItems=6]="UnregisterItems",r[r.SetButtonElement=7]="SetButtonElement",r[r.SetItemsElement=8]="SetItemsElement",r[r.SortItems=9]="SortItems",r))(F||{});function S(e,i=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,n=R(i(e.items.slice()),l=>l.dataRef.current.domRef.current),s=t?n.indexOf(t):null;return s===-1&&(s=null),{items:n,activeItemIndex:s}}let D={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,pendingFocus:{focus:c.Nothing},menuState:1}},[0](e,i){return e.menuState===0?e:{...e,__demoMode:!1,pendingFocus:i.focus,menuState:0}},[2]:(e,i)=>{var l,o,d,a,I;if(e.menuState===1)return e;let t={...e,searchQuery:"",activationTrigger:(l=i.trigger)!=null?l:1,__demoMode:!1};if(i.focus===c.Nothing)return{...t,activeItemIndex:null};if(i.focus===c.Specific)return{...t,activeItemIndex:e.items.findIndex(r=>r.id===i.id)};if(i.focus===c.Previous){let r=e.activeItemIndex;if(r!==null){let p=e.items[r].dataRef.current.domRef,m=f(i,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});if(m!==null){let u=e.items[m].dataRef.current.domRef;if(((o=p.current)==null?void 0:o.previousElementSibling)===u.current||((d=u.current)==null?void 0:d.previousElementSibling)===null)return{...t,activeItemIndex:m}}}}else if(i.focus===c.Next){let r=e.activeItemIndex;if(r!==null){let p=e.items[r].dataRef.current.domRef,m=f(i,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});if(m!==null){let u=e.items[m].dataRef.current.domRef;if(((a=p.current)==null?void 0:a.nextElementSibling)===u.current||((I=u.current)==null?void 0:I.nextElementSibling)===null)return{...t,activeItemIndex:m}}}}let n=S(e),s=f(i,{resolveItems:()=>n.items,resolveActiveIndex:()=>n.activeItemIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled});return{...t,...n,activeItemIndex:s}},[3]:(e,i)=>{let n=e.searchQuery!==""?0:1,s=e.searchQuery+i.value.toLowerCase(),o=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find(a=>{var I;return((I=a.dataRef.current.textValue)==null?void 0:I.startsWith(s))&&!a.dataRef.current.disabled}),d=o?e.items.indexOf(o):-1;return d===-1||d===e.activeItemIndex?{...e,searchQuery:s}:{...e,searchQuery:s,activeItemIndex:d,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,i)=>{let t=e.items.concat(i.items.map(s=>s)),n=e.activeItemIndex;return e.pendingFocus.focus!==c.Nothing&&(n=f(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeItemIndex,resolveId:s=>s.id,resolveDisabled:s=>s.dataRef.current.disabled})),{...e,items:t,activeItemIndex:n,pendingFocus:{focus:c.Nothing},pendingShouldSort:!0}},[6]:(e,i)=>{let t=e.items,n=[],s=new Set(i.items);for(let[l,o]of t.entries())if(s.has(o.id)&&(n.push(l),s.delete(o.id),s.size===0))break;if(n.length>0){t=t.slice();for(let l of n.reverse())t.splice(l,1)}return{...e,items:t,activationTrigger:1}},[7]:(e,i)=>e.buttonElement===i.element?e:{...e,buttonElement:i.element},[8]:(e,i)=>e.itemsElement===i.element?e:{...e,itemsElement:i.element},[9]:e=>e.pendingShouldSort?{...e,...S(e),pendingShouldSort:!1}:e};class x extends A{constructor(t){super(t);g(this,"actions",{registerItem:v(()=>{let t=[],n=new Set;return[(s,l)=>{n.has(l)||(n.add(l),t.push({id:s,dataRef:l}))},()=>(n.clear(),this.send({type:5,items:t.splice(0)}))]}),unregisterItem:v(()=>{let t=[];return[n=>t.push(n),()=>this.send({type:6,items:t.splice(0)})]})});g(this,"selectors",{activeDescendantId(t){var l;let n=t.activeItemIndex,s=t.items;return n===null||(l=s[n])==null?void 0:l.id},isActive(t,n){var o;let s=t.activeItemIndex,l=t.items;return s!==null?((o=l[s])==null?void 0:o.id)===n:!1},shouldScrollIntoView(t,n){return t.__demoMode||t.menuState!==0||t.activationTrigger===0?!1:this.isActive(t,n)}});this.on(5,()=>{this.disposables.requestAnimationFrame(()=>{this.send({type:9})})});{let n=this.state.id,s=T.get(null);this.disposables.add(s.on(M.Push,l=>{!s.selectors.isTop(l,n)&&this.state.menuState===0&&this.send({type:1})})),this.on(0,()=>s.actions.push(n)),this.on(1,()=>s.actions.pop(n))}}static new({id:t,__demoMode:n=!1}){return new x({id:t,__demoMode:n,menuState:n?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:c.Nothing}})}reduce(t,n){return b(n.type,D,t,n)}}export{F as ActionTypes,O as ActivationTrigger,x as MenuMachine,E as MenuState};
