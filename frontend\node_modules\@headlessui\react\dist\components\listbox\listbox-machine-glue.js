import{createContext as n,useContext as r,useMemo as i}from"react";import{useOnUnmount as s}from'../../hooks/use-on-unmount.js';import{ListboxMachine as a}from'./listbox-machine.js';const c=n(null);function p(o){let e=r(c);if(e===null){let t=new Error(`<${o} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}return e}function u({id:o,__demoMode:e=!1}){let t=i(()=>a.new({id:o,__demoMode:e}),[]);return s(()=>t.dispose()),t}export{c as ListboxContext,u as useListboxMachine,p as useListboxMachineContext};
