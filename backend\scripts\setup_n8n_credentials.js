const axios = require('axios');

// Configuración
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_USERNAME = '<EMAIL>';
const N8N_PASSWORD = 'admin123';

// API Key existente (si ya tienes una)
const EXISTING_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZjM0ODNlMC0xMjViLTRjMGItYTI4ZS1mNGJkMTJlYTdmNzAiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4MzI2Mzg0fQ.YHpG_nNe3ssUgOT7JmYm4a5o8VV221oQZHM3F4bIzzM';

// Función para hacer login y obtener cookie de sesión
async function loginToN8n() {
  try {
    console.log('Intentando hacer login en n8n...');

    const loginResponse = await axios.post(`${N8N_BASE_URL}/rest/login`, {
      emailOrLdapLoginId: N8N_USERNAME,
      password: N8N_PASSWORD
    }, {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (loginResponse.status === 200) {
      console.log('Login exitoso en n8n');

      // Extraer cookies de la respuesta
      const cookies = loginResponse.headers['set-cookie'];
      return cookies;
    } else {
      throw new Error('Login fallido');
    }
  } catch (error) {
    console.error('Error en login:', error.response?.data || error.message);
    throw error;
  }
}

// Función para crear API key
async function createApiKey(cookies) {
  try {
    console.log('Creando API key...');

    const cookieString = cookies.join('; ');

    const response = await axios.post(`${N8N_BASE_URL}/rest/api-keys`, {
      label: 'CCJAP System API Key'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookieString
      }
    });

    if (response.status === 201) {
      console.log('API key creada exitosamente');
      return response.data.apiKey;
    } else {
      throw new Error('Error creando API key');
    }
  } catch (error) {
    console.error('Error creando API key:', error.response?.data || error.message);
    throw error;
  }
}

// Función para obtener API keys existentes
async function getApiKeys(cookies) {
  try {
    console.log('Obteniendo API keys existentes...');

    const cookieString = cookies.join('; ');

    const response = await axios.get(`${N8N_BASE_URL}/rest/api-keys`, {
      headers: {
        'Cookie': cookieString
      }
    });

    if (response.status === 200) {
      console.log('API keys obtenidas exitosamente');
      return response.data;
    } else {
      throw new Error('Error obteniendo API keys');
    }
  } catch (error) {
    console.error('Error obteniendo API keys:', error.response?.data || error.message);
    return [];
  }
}

// Función para crear webhook básico
async function createBasicWorkflow(apiKey) {
  try {
    console.log('Creando workflow básico...');

    const workflowData = {
      name: 'WhatsApp Message Handler',
      active: true,
      nodes: [
        {
          parameters: {
            httpMethod: 'POST',
            path: 'whatsapp-webhook',
            responseMode: 'responseNode',
            options: {}
          },
          id: 'webhook-node',
          name: 'Webhook',
          type: 'n8n-nodes-base.webhook',
          typeVersion: 1,
          position: [240, 300]
        },
        {
          parameters: {
            respondWith: 'json',
            responseBody: '{\n  "status": "received",\n  "message": "Webhook processed successfully"\n}'
          },
          id: 'respond-node',
          name: 'Respond to Webhook',
          type: 'n8n-nodes-base.respondToWebhook',
          typeVersion: 1,
          position: [460, 300]
        }
      ],
      connections: {
        'Webhook': {
          main: [
            [
              {
                node: 'Respond to Webhook',
                type: 'main',
                index: 0
              }
            ]
          ]
        }
      }
    };

    const response = await axios.post(`${N8N_BASE_URL}/api/v1/workflows`, workflowData, {
      headers: {
        'Content-Type': 'application/json',
        'X-N8N-API-KEY': apiKey
      }
    });

    if (response.status === 201) {
      console.log('Workflow básico creado exitosamente');
      return response.data;
    } else {
      throw new Error('Error creando workflow');
    }
  } catch (error) {
    console.error('Error creando workflow:', error.response?.data || error.message);
    throw error;
  }
}

// Función para probar API key existente
async function testExistingApiKey(apiKey) {
  try {
    console.log('Probando API key existente...');

    const response = await axios.get(`${N8N_BASE_URL}/api/v1/workflows`, {
      headers: {
        'X-N8N-API-KEY': apiKey
      }
    });

    if (response.status === 200) {
      console.log('API key existente funciona correctamente');
      return true;
    }
    return false;
  } catch (error) {
    console.log('API key existente no funciona:', error.response?.data || error.message);
    return false;
  }
}

// Función principal
async function setupN8nCredentials() {
  try {
    console.log('=== Configuración de n8n ===');

    let apiKey = EXISTING_API_KEY;

    // 1. Probar API key existente primero
    const existingKeyWorks = await testExistingApiKey(EXISTING_API_KEY);

    if (existingKeyWorks) {
      console.log('Usando API key existente que funciona');
    } else {
      console.log('API key existente no funciona, intentando crear nueva...');

      try {
        // 2. Login
        const cookies = await loginToN8n();

        // 3. Verificar API keys existentes
        const existingKeys = await getApiKeys(cookies);

        if (existingKeys.length > 0) {
          console.log('API keys existentes encontradas:');
          existingKeys.forEach((key, index) => {
            console.log(`${index + 1}. ${key.label} (creada: ${key.createdAt})`);
          });

          // Usar la primera API key existente
          apiKey = existingKeys[0].apiKey;
          console.log('Usando API key existente');
        } else {
          // 4. Crear nueva API key
          apiKey = await createApiKey(cookies);
        }
      } catch (loginError) {
        console.log('No se pudo hacer login, pero continuando con API key existente...');
        apiKey = EXISTING_API_KEY;
      }
    }

    console.log('\n=== CREDENCIALES DE N8N ===');
    console.log(`URL: ${N8N_BASE_URL}`);
    console.log(`API Key: ${apiKey}`);
    console.log(`Username: ${N8N_USERNAME}`);
    console.log(`Password: ${N8N_PASSWORD}`);

    // 4. Crear workflow básico
    try {
      const workflow = await createBasicWorkflow(apiKey);
      console.log(`\nWorkflow creado con ID: ${workflow.id}`);
      console.log(`Webhook URL: ${N8N_BASE_URL}/webhook/whatsapp-webhook`);
    } catch (workflowError) {
      console.log('Workflow básico no pudo ser creado, pero las credenciales están listas');
    }

    console.log('\n=== CONFIGURACIÓN COMPLETADA ===');
    console.log('Puedes usar estas credenciales en la configuración de WaApi');

    return {
      url: N8N_BASE_URL,
      apiKey: apiKey,
      username: N8N_USERNAME,
      password: N8N_PASSWORD
    };

  } catch (error) {
    console.error('Error en la configuración:', error);
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  setupN8nCredentials()
    .then((credentials) => {
      console.log('\nCredenciales listas para usar');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error en la configuración:', error);
      process.exit(1);
    });
}

module.exports = { setupN8nCredentials };
