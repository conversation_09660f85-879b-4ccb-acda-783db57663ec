import{onDocumentReady as d}from'./document-ready.js';import*as u from'./dom.js';import{focusableSelector as i}from'./focus-management.js';let n=[];d(()=>{function e(t){if(!u.isHTMLorSVGElement(t.target)||t.target===document.body||n[0]===t.target)return;let r=t.target;r=r.closest(i),n.unshift(r!=null?r:t.target),n=n.filter(o=>o!=null&&o.isConnected),n.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});export{n as history};
