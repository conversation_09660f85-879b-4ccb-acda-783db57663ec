const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware, authorizeRoles } = require('../middleware/authMiddleware');

// Almacenar conexiones WebSocket activas
let wsConnections = new Map();

// POST /api/dashboard/notify - Recibir notificaciones de n8n
router.post('/notify', async (req, res) => {
  try {
    const { type, message } = req.body;
    
    console.log('Notificación recibida de n8n:', { type, message });
    
    // Procesar según el tipo de notificación
    if (type === 'new_whatsapp_message') {
      // Enviar notificación a todos los clientes conectados
      broadcastToClients({
        type: 'new_whatsapp_message',
        message: {
          id: message.message_id,
          phone_number: message.phone_number,
          content: message.original_message,
          sender_name: message.phone_number,
          created_at: message.timestamp,
          processed: false,
          message_type: message.type,
          requires_attention: message.requires_director_attention,
          ai_analysis: message
        }
      });
      
      // Reproducir sonido de notificación
      broadcastToClients({
        type: 'play_notification_sound',
        data: {
          message_type: message.type,
          requires_attention: message.requires_director_attention
        }
      });
    }
    
    res.json({ success: true, message: 'Notificación procesada' });
    
  } catch (error) {
    console.error('Error procesando notificación:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/dashboard/messages - Obtener mensajes para el dashboard
router.get('/messages', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const { filter, limit = 50, offset = 0 } = req.query;
    
    let query = `
      SELECT 
        id,
        telefono_remitente as phone_number,
        texto_mensaje as content,
        fecha_recepcion as created_at,
        procesado as processed,
        message_type,
        requires_attention,
        ai_analysis,
        telefono_remitente as sender_name
      FROM mensajes_whatsapp
      WHERE institucion_id = $1
    `;
    
    const values = [req.user.institucion_id];
    
    // Aplicar filtros
    if (filter === 'pending') {
      query += ' AND (procesado = false OR requires_attention = true)';
    } else if (filter === 'absence') {
      query += ' AND message_type = $2';
      values.push('absence');
    } else if (filter === 'question') {
      query += ' AND message_type = $2';
      values.push('question');
    } else if (filter === 'director_inquiry') {
      query += ' AND message_type = $2';
      values.push('director_inquiry');
    }
    
    query += ` ORDER BY fecha_recepcion DESC LIMIT $${values.length + 1} OFFSET $${values.length + 2}`;
    values.push(limit, offset);
    
    const result = await db.query(query, values);
    
    const messages = result.rows.map(row => ({
      id: row.id,
      phone_number: row.phone_number,
      sender_name: row.sender_name,
      content: row.content,
      created_at: row.created_at,
      processed: row.processed,
      message_type: row.message_type,
      requires_attention: row.requires_attention,
      ai_analysis: row.ai_analysis ? (typeof row.ai_analysis === 'string' ? JSON.parse(row.ai_analysis) : row.ai_analysis) : null
    }));
    
    res.json(messages);
    
  } catch (error) {
    console.error('Error obteniendo mensajes:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// PATCH /api/dashboard/messages/:id/process - Marcar mensaje como procesado
router.patch('/messages/:id/process', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const { id } = req.params;
    
    await db.query(
      'UPDATE mensajes_whatsapp SET procesado = true WHERE id = $1 AND institucion_id = $2',
      [id, req.user.institucion_id]
    );
    
    res.json({ success: true, message: 'Mensaje marcado como procesado' });
    
  } catch (error) {
    console.error('Error marcando mensaje como procesado:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/dashboard/send-reply - Enviar respuesta a WhatsApp
router.post('/send-reply', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const { phone_number, message, reply_to } = req.body;
    
    // Guardar la respuesta en la base de datos
    await db.query(
      `INSERT INTO mensajes_enviados (
        telefono_destinatario,
        contenido,
        tipo,
        usuario_id,
        institucion_id,
        respuesta_a
      ) VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        phone_number,
        message,
        'respuesta_manual',
        req.user.id,
        req.user.institucion_id,
        reply_to
      ]
    );
    
    // Aquí enviarías el mensaje real a través de la API de WhatsApp
    // Por ahora solo simulamos el envío
    
    // Marcar mensaje original como procesado
    if (reply_to) {
      await db.query(
        'UPDATE mensajes_whatsapp SET procesado = true WHERE id = $1',
        [reply_to]
      );
    }
    
    res.json({ success: true, message: 'Respuesta enviada correctamente' });
    
  } catch (error) {
    console.error('Error enviando respuesta:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/dashboard/stats - Obtener estadísticas del dashboard
router.get('/stats', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const stats = {};
    
    // Total de mensajes hoy
    const todayMessages = await db.query(
      `SELECT COUNT(*) as count FROM mensajes_whatsapp 
       WHERE DATE(fecha_recepcion) = CURRENT_DATE AND institucion_id = $1`,
      [req.user.institucion_id]
    );
    stats.messages_today = parseInt(todayMessages.rows[0].count);
    
    // Mensajes pendientes
    const pendingMessages = await db.query(
      `SELECT COUNT(*) as count FROM mensajes_whatsapp 
       WHERE (procesado = false OR requires_attention = true) AND institucion_id = $1`,
      [req.user.institucion_id]
    );
    stats.pending_messages = parseInt(pendingMessages.rows[0].count);
    
    // Ausencias reportadas hoy
    const todayAbsences = await db.query(
      `SELECT COUNT(*) as count FROM ausencias 
       WHERE DATE(fecha_ausencia) = CURRENT_DATE`
    );
    stats.absences_today = parseInt(todayAbsences.rows[0].count);
    
    // Mensajes por tipo
    const messagesByType = await db.query(
      `SELECT message_type, COUNT(*) as count FROM mensajes_whatsapp 
       WHERE institucion_id = $1 AND message_type IS NOT NULL
       GROUP BY message_type`,
      [req.user.institucion_id]
    );
    stats.messages_by_type = messagesByType.rows.reduce((acc, row) => {
      acc[row.message_type] = parseInt(row.count);
      return acc;
    }, {});
    
    res.json(stats);
    
  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/dashboard/absence-reports - Obtener reportes de ausencia
router.get('/absence-reports', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const { limit = 20, offset = 0 } = req.query;
    
    const query = `
      SELECT 
        a.*,
        al.nombre_completo as student_name,
        al.grado_actual as student_grade,
        u.nombre as teacher_name
      FROM ausencias a
      LEFT JOIN alumnos al ON a.alumno_id = al.id
      LEFT JOIN usuarios u ON al.maestro_id = u.id
      WHERE a.reportado_por_telefono IS NOT NULL
      ORDER BY a.fecha_ausencia DESC
      LIMIT $1 OFFSET $2
    `;
    
    const result = await db.query(query, [limit, offset]);
    
    const reports = result.rows.map(row => ({
      id: row.id,
      student_name: row.student_name || 'Estudiante no identificado',
      student_grade: row.student_grade || row.grado_reportado,
      absence_date: row.fecha_ausencia,
      reason: row.motivo,
      reported_by_phone: row.reportado_por_telefono,
      reported_by_name: row.reportado_por_nombre,
      teacher_name: row.teacher_name,
      justified: row.justificado,
      teacher_notified: row.notificado_docente,
      created_at: row.fecha_registro
    }));
    
    res.json(reports);
    
  } catch (error) {
    console.error('Error obteniendo reportes de ausencia:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Función para enviar mensajes a todos los clientes conectados
function broadcastToClients(data) {
  wsConnections.forEach((ws, clientId) => {
    if (ws.readyState === 1) { // WebSocket.OPEN
      try {
        ws.send(JSON.stringify(data));
      } catch (error) {
        console.error('Error enviando mensaje WebSocket:', error);
        wsConnections.delete(clientId);
      }
    } else {
      wsConnections.delete(clientId);
    }
  });
}

// Función para configurar WebSocket (se llamará desde server.js)
function setupWebSocket(server) {
  const WebSocket = require('ws');
  const wss = new WebSocket.Server({ server });
  
  wss.on('connection', (ws, req) => {
    const clientId = Date.now() + Math.random();
    wsConnections.set(clientId, ws);
    
    console.log(`Cliente WebSocket conectado: ${clientId}`);
    
    ws.on('close', () => {
      wsConnections.delete(clientId);
      console.log(`Cliente WebSocket desconectado: ${clientId}`);
    });
    
    ws.on('error', (error) => {
      console.error('Error en WebSocket:', error);
      wsConnections.delete(clientId);
    });
    
    // Enviar mensaje de bienvenida
    ws.send(JSON.stringify({
      type: 'connection_established',
      message: 'Conectado al dashboard de WhatsApp'
    }));
  });
  
  return wss;
}

module.exports = router;
module.exports.setupWebSocket = setupWebSocket;
module.exports.broadcastToClients = broadcastToClients;
