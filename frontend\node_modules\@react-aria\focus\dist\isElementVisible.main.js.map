{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AAID,SAAS,qCAAe,OAAgB;IACtC,MAAM,eAAe,CAAA,GAAA,oCAAa,EAAE;IACpC,IAAI,CAAE,CAAA,mBAAmB,aAAa,WAAW,AAAD,KAAM,CAAE,CAAA,mBAAmB,aAAa,UAAU,AAAD,GAC/F,OAAO;IAGT,IAAI,WAAC,OAAO,cAAE,UAAU,EAAC,GAAG,QAAQ,KAAK;IAEzC,IAAI,YACF,YAAY,UACZ,eAAe,YACf,eAAe;IAGjB,IAAI,WAAW;QACb,MAAM,oBAAC,gBAAgB,EAAC,GAAG,QAAQ,aAAa,CAAC,WAAW;QAC5D,IAAI,EAAC,SAAS,eAAe,EAAE,YAAY,kBAAkB,EAAC,GAAG,iBAAiB;QAElF,YACE,oBAAoB,UACpB,uBAAuB,YACvB,uBAAuB;IAE3B;IAEA,OAAO;AACT;AAEA,SAAS,yCAAmB,OAAgB,EAAE,YAAsB;IAClE,OACE,CAAC,QAAQ,YAAY,CAAC,aACtB,yCAAyC;IACzC,CAAC,QAAQ,YAAY,CAAC,oCACrB,CAAA,QAAQ,QAAQ,KAAK,aACpB,gBACA,aAAa,QAAQ,KAAK,YACxB,QAAQ,YAAY,CAAC,UACrB,IAAG;AAEX;AAQO,SAAS,0CAAiB,OAAgB,EAAE,YAAsB;IACvE,OACE,QAAQ,QAAQ,KAAK,cACrB,qCAAe,YACf,yCAAmB,SAAS,iBAC3B,CAAA,CAAC,QAAQ,aAAa,IAAI,0CAAiB,QAAQ,aAAa,EAAE,QAAO;AAE9E", "sources": ["packages/@react-aria/focus/src/isElementVisible.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerWindow} from '@react-aria/utils';\n\nfunction isStyleVisible(element: Element) {\n  const windowObject = getOwnerWindow(element);\n  if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) {\n    return false;\n  }\n\n  let {display, visibility} = element.style;\n\n  let isVisible = (\n    display !== 'none' &&\n    visibility !== 'hidden' &&\n    visibility !== 'collapse'\n  );\n\n  if (isVisible) {\n    const {getComputedStyle} = element.ownerDocument.defaultView as unknown as Window;\n    let {display: computedDisplay, visibility: computedVisibility} = getComputedStyle(element);\n\n    isVisible = (\n      computedDisplay !== 'none' &&\n      computedVisibility !== 'hidden' &&\n      computedVisibility !== 'collapse'\n    );\n  }\n\n  return isVisible;\n}\n\nfunction isAttributeVisible(element: Element, childElement?: Element) {\n  return (\n    !element.hasAttribute('hidden') &&\n    // Ignore HiddenSelect when tree walking.\n    !element.hasAttribute('data-react-aria-prevent-focus') &&\n    (element.nodeName === 'DETAILS' &&\n      childElement &&\n      childElement.nodeName !== 'SUMMARY'\n      ? element.hasAttribute('open')\n      : true)\n  );\n}\n\n/**\n * Adapted from https://github.com/testing-library/jest-dom and\n * https://github.com/vuejs/vue-test-utils-next/.\n * Licensed under the MIT License.\n * @param element - Element to evaluate for display or visibility.\n */\nexport function isElementVisible(element: Element, childElement?: Element): boolean {\n  return (\n    element.nodeName !== '#comment' &&\n    isStyleVisible(element) &&\n    isAttributeVisible(element, childElement) &&\n    (!element.parentElement || isElementVisible(element.parentElement, element))\n  );\n}\n"], "names": [], "version": 3, "file": "isElementVisible.main.js.map"}