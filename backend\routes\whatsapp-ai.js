const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware, authorizeRoles } = require('../middleware/authMiddleware');
const axios = require('axios');

// Función para analizar mensaje con IA (simulada)
async function analyzeMessageWithAI(message, senderPhone) {
  try {
    // Aquí integrarías con OpenAI, <PERSON>, o cualquier otra IA
    // Por ahora simularemos el análisis

    const messageText = message.toLowerCase();
    let analysis = {
      type: 'general',
      intent: 'unknown',
      confidence: 0.5,
      suggested_response: null,
      requires_human_attention: false,
      extracted_data: {}
    };

    // Detectar ausencias
    const absenceKeywords = ['ausencia', 'falta', 'no va', 'no ira', 'no asistira', 'enfermo', 'no puede ir'];
    if (absenceKeywords.some(keyword => messageText.includes(keyword))) {
      analysis.type = 'absence';
      analysis.intent = 'report_absence';
      analysis.confidence = 0.9;
      analysis.requires_human_attention = true;
      analysis.suggested_response = 'Entendido. Para procesar la ausencia, necesito el nombre completo del estudiante y su grado. ¿Podrías proporcionarme esta información?';

      // Intentar extraer nombre del estudiante
      const nameMatch = messageText.match(/(?:mi hijo|mi hija|el estudiante|la estudiante)\s+([a-záéíóúñ\s]+)/i);
      if (nameMatch) {
        analysis.extracted_data.student_name = nameMatch[1].trim();
      }
    }

    // Detectar preguntas comunes
    const questionKeywords = ['hay clases', 'horario', 'que hora', 'cuando', 'donde esta', 'esta el director'];
    if (questionKeywords.some(keyword => messageText.includes(keyword))) {
      analysis.type = 'question';
      analysis.intent = 'general_inquiry';
      analysis.confidence = 0.8;

      if (messageText.includes('hay clases')) {
        analysis.suggested_response = 'Sí, hay clases normalmente. El horario es de 7:00 AM a 12:00 PM. Si hay algún cambio, se notificará con anticipación.';
      } else if (messageText.includes('horario') || messageText.includes('que hora')) {
        analysis.suggested_response = 'El horario de clases es de 7:00 AM a 12:00 PM de lunes a viernes. La oficina administrativa atiende de 7:00 AM a 3:00 PM.';
      } else if (messageText.includes('director')) {
        analysis.suggested_response = 'El director está disponible para atención de 8:00 AM a 2:00 PM. ¿En qué puedo ayudarle?';
        analysis.requires_human_attention = true;
      }
    }

    // Detectar quejas o problemas
    const complaintKeywords = ['problema', 'queja', 'molesto', 'disgusto', 'mal servicio'];
    if (complaintKeywords.some(keyword => messageText.includes(keyword))) {
      analysis.type = 'complaint';
      analysis.intent = 'complaint';
      analysis.confidence = 0.85;
      analysis.requires_human_attention = true;
      analysis.suggested_response = 'Lamento escuchar sobre su preocupación. Un miembro de nuestro equipo se pondrá en contacto con usted pronto para resolver este asunto.';
    }

    return analysis;
  } catch (error) {
    console.error('Error en análisis de IA:', error);
    return {
      type: 'general',
      intent: 'unknown',
      confidence: 0.1,
      suggested_response: null,
      requires_human_attention: true,
      extracted_data: {}
    };
  }
}

// Función para procesar ausencia automáticamente
async function processAbsenceReport(messageData, analysis) {
  try {
    const { phone_number, content, sender_name } = messageData;

    // Buscar si ya existe un proceso de ausencia en curso para este número
    const existingProcess = await db.query(
      'SELECT * FROM absence_processes WHERE phone_number = $1 AND status = $2',
      [phone_number, 'pending_info']
    );

    if (existingProcess.rows.length > 0) {
      // Continuar proceso existente
      const process = existingProcess.rows[0];

      // Intentar extraer información faltante
      let updatedData = { ...process.collected_data };

      if (!updatedData.student_name && analysis.extracted_data.student_name) {
        updatedData.student_name = analysis.extracted_data.student_name;
      }

      // Si tenemos nombre pero no grado, preguntar por grado
      if (updatedData.student_name && !updatedData.grade) {
        const gradeMatch = content.toLowerCase().match(/grado\s*(\d+|primero|segundo|tercero|cuarto|quinto|sexto)/i);
        if (gradeMatch) {
          updatedData.grade = gradeMatch[1];
        }
      }

      // Actualizar proceso
      await db.query(
        'UPDATE absence_processes SET collected_data = $1, last_message = $2 WHERE id = $3',
        [JSON.stringify(updatedData), content, process.id]
      );

      // Si tenemos toda la información, crear el reporte
      if (updatedData.student_name && updatedData.grade) {
        await createAbsenceReport(process.id, updatedData, phone_number, sender_name);
        return {
          action: 'absence_created',
          message: `Perfecto. He registrado la ausencia de ${updatedData.student_name} de ${updatedData.grade} grado. Se ha notificado al docente correspondiente y se generará un reporte.`
        };
      } else {
        // Pedir información faltante
        let missingInfo = [];
        if (!updatedData.student_name) missingInfo.push('nombre completo del estudiante');
        if (!updatedData.grade) missingInfo.push('grado');

        return {
          action: 'request_info',
          message: `Para completar el reporte de ausencia, necesito: ${missingInfo.join(' y ')}. ¿Podrías proporcionarme esta información?`
        };
      }
    } else {
      // Crear nuevo proceso de ausencia
      const processResult = await db.query(
        `INSERT INTO absence_processes (phone_number, sender_name, initial_message, collected_data, status)
         VALUES ($1, $2, $3, $4, $5) RETURNING id`,
        [
          phone_number,
          sender_name,
          content,
          JSON.stringify(analysis.extracted_data),
          'pending_info'
        ]
      );

      return {
        action: 'start_process',
        message: analysis.suggested_response
      };
    }
  } catch (error) {
    console.error('Error procesando ausencia:', error);
    return {
      action: 'error',
      message: 'Disculpe, hubo un error procesando su solicitud. Un miembro del equipo se pondrá en contacto con usted.'
    };
  }
}

// Función para crear reporte de ausencia completo
async function createAbsenceReport(processId, studentData, parentPhone, parentName) {
  try {
    // Buscar estudiante en la base de datos
    const studentResult = await db.query(
      'SELECT * FROM alumnos WHERE nombre_completo ILIKE $1',
      [`%${studentData.student_name}%`]
    );

    let studentId = null;
    if (studentResult.rows.length > 0) {
      studentId = studentResult.rows[0].id;
    }

    // Crear registro de ausencia
    const absenceResult = await db.query(
      `INSERT INTO ausencias (
        alumno_id,
        fecha_ausencia,
        motivo,
        reportado_por_telefono,
        reportado_por_nombre,
        grado_reportado,
        proceso_id,
        justificado
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id`,
      [
        studentId,
        new Date(),
        `Ausencia reportada por WhatsApp: ${studentData.reason || 'No especificado'}`,
        parentPhone,
        parentName,
        studentData.grade,
        processId,
        true
      ]
    );

    // Actualizar proceso como completado
    await db.query(
      'UPDATE absence_processes SET status = $1, absence_id = $2 WHERE id = $3',
      ['completed', absenceResult.rows[0].id, processId]
    );

    // Notificar al docente (si encontramos al estudiante)
    if (studentId && studentResult.rows[0].maestro_id) {
      await notifyTeacher(studentResult.rows[0].maestro_id, absenceResult.rows[0].id, studentData);
    }

    // Generar PDF del reporte (implementar después)
    // await generateAbsenceReportPDF(absenceResult.rows[0].id);

    return absenceResult.rows[0].id;
  } catch (error) {
    console.error('Error creando reporte de ausencia:', error);
    throw error;
  }
}

// Función para notificar al docente
async function notifyTeacher(teacherId, absenceId, studentData) {
  try {
    const teacherResult = await db.query(
      'SELECT nombre, email, telefono FROM usuarios WHERE id = $1',
      [teacherId]
    );

    if (teacherResult.rows.length > 0) {
      const teacher = teacherResult.rows[0];

      // Enviar notificación por WhatsApp al docente (si tiene teléfono)
      if (teacher.telefono) {
        const message = `🔔 NOTIFICACIÓN DE AUSENCIA\n\nEstudiante: ${studentData.student_name}\nGrado: ${studentData.grade}\nFecha: ${new Date().toLocaleDateString()}\nReportado por: Padre de familia vía WhatsApp\n\nID de reporte: ${absenceId}`;

        // Aquí enviarías el mensaje al docente
        // await sendWhatsAppMessage(teacher.telefono, message);
      }

      // También podrías enviar email
      // await sendEmailNotification(teacher.email, studentData, absenceId);
    }
  } catch (error) {
    console.error('Error notificando al docente:', error);
  }
}

// POST /api/whatsapp-ai/process-message - Procesar mensaje con IA
router.post('/process-message', authMiddleware, async (req, res) => {
  try {
    const { phone_number, message, sender_name, message_id } = req.body;

    // Analizar mensaje con IA
    const analysis = await analyzeMessageWithAI(message, phone_number);

    // Guardar análisis en la base de datos
    await db.query(
      `UPDATE mensajes_whatsapp
       SET ai_analysis = $1, message_type = $2, requires_attention = $3
       WHERE id = $4`,
      [
        JSON.stringify(analysis),
        analysis.type,
        analysis.requires_human_attention,
        message_id
      ]
    );

    let autoResponse = null;

    // Procesar según el tipo de mensaje
    if (analysis.type === 'absence') {
      const absenceResult = await processAbsenceReport({
        phone_number,
        content: message,
        sender_name
      }, analysis);

      autoResponse = absenceResult.message;
    } else if (analysis.type === 'question' && analysis.suggested_response) {
      autoResponse = analysis.suggested_response;
    }

    res.json({
      success: true,
      analysis,
      auto_response: autoResponse,
      requires_human_attention: analysis.requires_human_attention
    });

  } catch (error) {
    console.error('Error procesando mensaje con IA:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/whatsapp-ai/messages - Obtener mensajes con análisis
router.get('/messages', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const { filter, limit = 50 } = req.query;

    let query = `
      SELECT
        mw.*,
        ai_analysis,
        message_type,
        requires_attention
      FROM mensajes_whatsapp mw
      WHERE mw.institucion_id = $1
    `;

    const values = [req.user.institucion_id];

    if (filter === 'pending') {
      query += ' AND (mw.procesado = false OR requires_attention = true)';
    } else if (filter === 'absence') {
      query += ' AND message_type = $2';
      values.push('absence');
    } else if (filter === 'question') {
      query += ' AND message_type = $2';
      values.push('question');
    }

    query += ' ORDER BY mw.fecha_recepcion DESC LIMIT $' + (values.length + 1);
    values.push(limit);

    const result = await db.query(query, values);

    const messages = result.rows.map(row => ({
      id: row.id,
      phone_number: row.telefono_remitente,
      sender_name: row.nombre_remitente || row.telefono_remitente,
      content: row.texto_mensaje,
      created_at: row.fecha_recepcion,
      processed: row.procesado,
      message_type: row.message_type,
      requires_attention: row.requires_attention,
      ai_analysis: row.ai_analysis ? JSON.parse(row.ai_analysis) : null
    }));

    res.json(messages);

  } catch (error) {
    console.error('Error obteniendo mensajes:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/whatsapp-ai/send-reply - Enviar respuesta
router.post('/send-reply', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const { phone_number, message, reply_to } = req.body;

    // Aquí integrarías con tu API de WhatsApp para enviar el mensaje
    // Por ahora solo guardamos la respuesta en la base de datos

    await db.query(
      `INSERT INTO mensajes_enviados (
        telefono_destinatario,
        contenido,
        tipo,
        usuario_id,
        institucion_id,
        respuesta_a
      ) VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        phone_number,
        message,
        'respuesta',
        req.user.id,
        req.user.institucion_id,
        reply_to
      ]
    );

    // Marcar mensaje original como procesado
    if (reply_to) {
      await db.query(
        'UPDATE mensajes_whatsapp SET procesado = true WHERE id = $1',
        [reply_to]
      );
    }

    res.json({ success: true, message: 'Respuesta enviada correctamente' });

  } catch (error) {
    console.error('Error enviando respuesta:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Función para inicializar tablas necesarias
async function initializeTables() {
  try {
    // Tabla para procesos de ausencia
    await db.query(`
      CREATE TABLE IF NOT EXISTS absence_processes (
        id SERIAL PRIMARY KEY,
        phone_number VARCHAR(25) NOT NULL,
        sender_name VARCHAR(255),
        initial_message TEXT,
        collected_data JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'pending_info',
        absence_id INTEGER REFERENCES ausencias(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Agregar columnas a mensajes_whatsapp si no existen
    await db.query(`
      ALTER TABLE mensajes_whatsapp
      ADD COLUMN IF NOT EXISTS ai_analysis JSONB,
      ADD COLUMN IF NOT EXISTS message_type VARCHAR(50),
      ADD COLUMN IF NOT EXISTS requires_attention BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS nombre_remitente VARCHAR(255)
    `);

    // Agregar columnas a ausencias si no existen
    await db.query(`
      ALTER TABLE ausencias
      ADD COLUMN IF NOT EXISTS reportado_por_telefono VARCHAR(25),
      ADD COLUMN IF NOT EXISTS reportado_por_nombre VARCHAR(255),
      ADD COLUMN IF NOT EXISTS grado_reportado VARCHAR(50),
      ADD COLUMN IF NOT EXISTS proceso_id INTEGER REFERENCES absence_processes(id)
    `);

    // Tabla para mensajes enviados si no existe
    await db.query(`
      CREATE TABLE IF NOT EXISTS mensajes_enviados (
        id SERIAL PRIMARY KEY,
        telefono_destinatario VARCHAR(25) NOT NULL,
        contenido TEXT NOT NULL,
        tipo VARCHAR(50) DEFAULT 'respuesta',
        usuario_id INTEGER REFERENCES usuarios(id),
        institucion_id INTEGER REFERENCES instituciones(id),
        respuesta_a INTEGER REFERENCES mensajes_whatsapp(id),
        fecha_envio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        estado VARCHAR(50) DEFAULT 'enviado'
      )
    `);

    console.log('Tablas de WhatsApp AI inicializadas correctamente');
  } catch (error) {
    console.error('Error inicializando tablas:', error);
  }
}

// Inicializar tablas al cargar el módulo
initializeTables();

module.exports = router;
