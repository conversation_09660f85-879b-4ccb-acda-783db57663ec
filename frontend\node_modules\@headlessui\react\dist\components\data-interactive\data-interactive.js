"use client";import{useFocusRing as m}from"@react-aria/focus";import{useHover as A}from"@react-aria/interactions";import{Fragment as D,useMemo as f}from"react";import{useActivePress as v}from'../../hooks/use-active-press.js';import{forwardRefWithAs as I,mergeProps as y,useRender as P}from'../../utils/render.js';let E=D;function d(o,n){let{...s}=o,e=!1,{isFocusVisible:t,focusProps:p}=m(),{isHovered:r,hoverProps:i}=A({isDisabled:e}),{pressed:a,pressProps:T}=v({disabled:e}),l=y({ref:n},p,i,T),c=f(()=>({hover:r,focus:t,active:a}),[r,t,a]);return P()({ourProps:l,theirProps:s,slot:c,defaultTag:E,name:"DataInteractive"})}let x=I(d);export{x as DataInteractive};
