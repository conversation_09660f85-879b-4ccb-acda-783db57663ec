"use client";import{useFocusRing as Q}from"@react-aria/focus";import{useHover as Y}from"@react-aria/interactions";import i,{Fragment as Z,createContext as ee,useCallback as te,useContext as oe,useMemo as R,useRef as re,useState as w}from"react";import{useActivePress as ne}from'../../hooks/use-active-press.js';import{useControllable as le}from'../../hooks/use-controllable.js';import{useDefaultValue as ie}from'../../hooks/use-default-value.js';import{useDisposables as ae}from'../../hooks/use-disposables.js';import{useEvent as f}from'../../hooks/use-event.js';import{useId as se}from'../../hooks/use-id.js';import{useResolveButtonType as pe}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as ce}from'../../hooks/use-sync-refs.js';import{useDisabled as ue}from'../../internal/disabled.js';import{FormFields as de}from'../../internal/form-fields.js';import{useProvidedId as me}from'../../internal/id.js';import{isDisabledReactIssue7711 as fe}from'../../utils/bugs.js';import*as he from'../../utils/dom.js';import{attemptSubmit as be}from'../../utils/form.js';import{forwardRefWithAs as Te,mergeProps as ye,useRender as G}from'../../utils/render.js';import{Description as Se,useDescribedBy as we,useDescriptions as Ee}from'../description/description.js';import{Keys as A}from'../keyboard.js';import{Label as _e,useLabelledBy as Pe,useLabels as De}from'../label/label.js';let E=ee(null);E.displayName="GroupContext";let ge=Z;function ve(n){var u;let[o,s]=w(null),[h,b]=De(),[T,t]=Ee(),p=R(()=>({switch:o,setSwitch:s}),[o,s]),y={},S=n,c=G();return i.createElement(t,{name:"Switch.Description",value:T},i.createElement(b,{name:"Switch.Label",value:h,props:{htmlFor:(u=p.switch)==null?void 0:u.id,onClick(d){o&&(he.isHTMLLabelElement(d.currentTarget)&&d.preventDefault(),o.click(),o.focus({preventScroll:!0}))}}},i.createElement(E.Provider,{value:p},c({ourProps:y,theirProps:S,slot:{},defaultTag:ge,name:"Switch.Group"}))))}let xe="button";function Ce(n,o){var L;let s=se(),h=me(),b=ue(),{id:T=h||`headlessui-switch-${s}`,disabled:t=b||!1,checked:p,defaultChecked:y,onChange:S,name:c,value:u,form:d,autoFocus:m=!1,...F}=n,_=oe(E),[H,k]=w(null),M=re(null),U=ce(M,o,_===null?null:_.setSwitch,k),l=ie(y),[a,r]=le(p,S,l!=null?l:!1),I=ae(),[P,D]=w(!1),g=f(()=>{D(!0),r==null||r(!a),I.nextFrame(()=>{D(!1)})}),B=f(e=>{if(fe(e.currentTarget))return e.preventDefault();e.preventDefault(),g()}),K=f(e=>{e.key===A.Space?(e.preventDefault(),g()):e.key===A.Enter&&be(e.currentTarget)}),O=f(e=>e.preventDefault()),W=Pe(),N=we(),{isFocusVisible:v,focusProps:J}=Q({autoFocus:m}),{isHovered:x,hoverProps:V}=Y({isDisabled:t}),{pressed:C,pressProps:X}=ne({disabled:t}),j=R(()=>({checked:a,disabled:t,hover:x,focus:v,active:C,autofocus:m,changing:P}),[a,x,v,C,t,P,m]),$=ye({id:T,ref:U,role:"switch",type:pe(n,H),tabIndex:n.tabIndex===-1?0:(L=n.tabIndex)!=null?L:0,"aria-checked":a,"aria-labelledby":W,"aria-describedby":N,disabled:t||void 0,autoFocus:m,onClick:B,onKeyUp:K,onKeyPress:O},J,V,X),q=te(()=>{if(l!==void 0)return r==null?void 0:r(l)},[r,l]),z=G();return i.createElement(i.Fragment,null,c!=null&&i.createElement(de,{disabled:t,data:{[c]:u||"on"},overrides:{type:"checkbox",checked:a},form:d,onReset:q}),z({ourProps:$,theirProps:F,slot:j,defaultTag:xe,name:"Switch"}))}let Le=Te(Ce),Re=ve,Ge=_e,Ae=Se,Ze=Object.assign(Le,{Group:Re,Label:Ge,Description:Ae});export{Ze as Switch,Ae as SwitchDescription,Re as SwitchGroup,Ge as SwitchLabel};
