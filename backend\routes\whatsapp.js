const express = require('express');
const router = express.Router();
const db = require('../config/db');
const authenticateToken = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configuración de multer para subida de archivos
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/whatsapp');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/webm', 'video/ogg',
      'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mpeg',
      'application/pdf', 'application/msword', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de archivo no permitido'), false);
    }
  }
});

// Verificar permisos
const verificarPermisos = (req, res, next) => {
  const rolesPermitidos = ['Director', 'Secretaria', 'Superadministrador', 'Director Academico'];
  if (!rolesPermitidos.includes(req.user.rol)) {
    return res.status(403).json({ error: 'No tienes permisos para acceder a esta funcionalidad' });
  }
  next();
};

// GET /api/whatsapp/conversaciones - Obtener lista de conversaciones
router.get('/conversaciones', authenticateToken, verificarPermisos, async (req, res) => {
  try {
    const query = `
      SELECT 
        telefono_remitente as telefono,
        MAX(fecha_recepcion) as ultima_actividad,
        COUNT(*) as total_mensajes,
        COUNT(CASE WHEN procesado = false THEN 1 END) as mensajes_no_leidos,
        (SELECT texto_mensaje FROM mensajes_whatsapp m2 
         WHERE m2.telefono_remitente = m1.telefono_remitente 
         ORDER BY fecha_recepcion DESC LIMIT 1) as ultimo_mensaje,
        (SELECT tipo_mensaje FROM mensajes_whatsapp m2 
         WHERE m2.telefono_remitente = m1.telefono_remitente 
         ORDER BY fecha_recepcion DESC LIMIT 1) as estado
      FROM mensajes_whatsapp m1
      WHERE institucion_id = $1
      GROUP BY telefono_remitente
      ORDER BY ultima_actividad DESC
    `;
    
    const result = await db.query(query, [req.user.institucion_id]);
    
    const conversaciones = result.rows.map(row => ({
      telefono: row.telefono,
      nombre: null, // Aquí podrías buscar el nombre en la tabla de alumnos/contactos
      ultimaActividad: new Date(row.ultima_actividad).toLocaleString(),
      ultimoMensaje: row.ultimo_mensaje,
      totalMensajes: parseInt(row.total_mensajes),
      mensajesNoLeidos: parseInt(row.mensajes_no_leidos),
      estado: row.mensajes_no_leidos > 0 ? 'pendiente' : 'leido'
    }));
    
    res.json(conversaciones);
  } catch (error) {
    console.error('Error al obtener conversaciones:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/whatsapp/mensajes/:telefono - Obtener mensajes de una conversación
router.get('/mensajes/:telefono', authenticateToken, verificarPermisos, async (req, res) => {
  try {
    const { telefono } = req.params;
    
    const query = `
      SELECT 
        id,
        telefono_remitente,
        texto_mensaje as contenido,
        fecha_recepcion as fecha_envio,
        tipo_mensaje as tipo,
        procesado,
        archivo_url,
        nombre_archivo,
        false as es_enviado
      FROM mensajes_whatsapp
      WHERE telefono_remitente = $1 AND institucion_id = $2
      
      UNION ALL
      
      SELECT 
        id,
        telefono_destinatario as telefono_remitente,
        contenido,
        fecha_envio,
        tipo,
        true as procesado,
        archivo_url,
        nombre_archivo,
        true as es_enviado
      FROM mensajes_enviados
      WHERE telefono_destinatario = $1 AND institucion_id = $2
      
      ORDER BY fecha_envio ASC
    `;
    
    const result = await db.query(query, [telefono, req.user.institucion_id]);
    
    const mensajes = result.rows.map(row => ({
      id: row.id,
      contenido: row.contenido,
      fechaEnvio: row.fecha_envio,
      tipo: row.tipo || 'texto',
      esEnviado: row.es_enviado,
      estado: row.es_enviado ? 'entregado' : null,
      archivoUrl: row.archivo_url,
      nombreArchivo: row.nombre_archivo
    }));
    
    res.json(mensajes);
  } catch (error) {
    console.error('Error al obtener mensajes:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/whatsapp/enviar - Enviar mensaje
router.post('/enviar', authenticateToken, verificarPermisos, upload.array('archivos', 10), async (req, res) => {
  try {
    const { telefono, mensaje, tipo = 'texto' } = req.body;
    const archivos = req.files || [];
    
    // Crear tabla de mensajes enviados si no existe
    await db.query(`
      CREATE TABLE IF NOT EXISTS mensajes_enviados (
        id SERIAL PRIMARY KEY,
        telefono_destinatario VARCHAR(25) NOT NULL,
        contenido TEXT,
        fecha_envio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        tipo VARCHAR(50) DEFAULT 'texto',
        archivo_url VARCHAR(2048),
        nombre_archivo VARCHAR(255),
        estado VARCHAR(50) DEFAULT 'enviado',
        usuario_id INTEGER REFERENCES usuarios(id),
        institucion_id INTEGER REFERENCES instituciones(id)
      )
    `);
    
    let archivoUrl = null;
    let nombreArchivo = null;
    
    if (archivos.length > 0) {
      const archivo = archivos[0]; // Por ahora solo el primer archivo
      archivoUrl = `/uploads/whatsapp/${archivo.filename}`;
      nombreArchivo = archivo.originalname;
    }
    
    const insertQuery = `
      INSERT INTO mensajes_enviados 
      (telefono_destinatario, contenido, tipo, archivo_url, nombre_archivo, usuario_id, institucion_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;
    
    const result = await db.query(insertQuery, [
      telefono,
      mensaje,
      tipo,
      archivoUrl,
      nombreArchivo,
      req.user.id,
      req.user.institucion_id
    ]);
    
    // Aquí integrarías con la API de WhatsApp para enviar realmente el mensaje
    // Por ahora solo guardamos en la base de datos
    
    res.json({
      success: true,
      mensaje: 'Mensaje enviado correctamente',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error al enviar mensaje:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/whatsapp/aprobar/:id - Aprobar mensaje
router.post('/aprobar/:id', authenticateToken, verificarPermisos, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Solo directores y superadministradores pueden aprobar
    if (!['Director', 'Superadministrador'].includes(req.user.rol)) {
      return res.status(403).json({ error: 'No tienes permisos para aprobar mensajes' });
    }
    
    const updateQuery = `
      UPDATE mensajes_whatsapp 
      SET procesado = true, 
          fecha_actualizacion = CURRENT_TIMESTAMP,
          aprobado_por = $1
      WHERE id = $2 AND institucion_id = $3
      RETURNING *
    `;
    
    const result = await db.query(updateQuery, [req.user.id, id, req.user.institucion_id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Mensaje no encontrado' });
    }
    
    res.json({
      success: true,
      mensaje: 'Mensaje aprobado correctamente',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error al aprobar mensaje:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/whatsapp/reenviar/:id - Reenviar mensaje
router.post('/reenviar/:id', authenticateToken, verificarPermisos, async (req, res) => {
  try {
    const { id } = req.params;
    const { telefonos } = req.body; // Array de teléfonos
    
    // Obtener el mensaje original
    const mensajeQuery = `
      SELECT * FROM mensajes_whatsapp 
      WHERE id = $1 AND institucion_id = $2
    `;
    
    const mensajeResult = await db.query(mensajeQuery, [id, req.user.institucion_id]);
    
    if (mensajeResult.rows.length === 0) {
      return res.status(404).json({ error: 'Mensaje no encontrado' });
    }
    
    const mensajeOriginal = mensajeResult.rows[0];
    
    // Reenviar a cada teléfono
    const promesas = telefonos.map(telefono => {
      const insertQuery = `
        INSERT INTO mensajes_enviados 
        (telefono_destinatario, contenido, tipo, archivo_url, nombre_archivo, usuario_id, institucion_id)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `;
      
      return db.query(insertQuery, [
        telefono,
        `[Reenviado] ${mensajeOriginal.texto_mensaje}`,
        mensajeOriginal.tipo_mensaje || 'texto',
        mensajeOriginal.archivo_url,
        mensajeOriginal.nombre_archivo,
        req.user.id,
        req.user.institucion_id
      ]);
    });
    
    await Promise.all(promesas);
    
    res.json({
      success: true,
      mensaje: `Mensaje reenviado a ${telefonos.length} contactos`
    });
  } catch (error) {
    console.error('Error al reenviar mensaje:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

module.exports = router;
