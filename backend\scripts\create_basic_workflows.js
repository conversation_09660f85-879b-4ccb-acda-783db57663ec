const axios = require('axios');

// Configuración
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZjM0ODNlMC0xMjViLTRjMGItYTI4ZS1mNGJkMTJlYTdmNzAiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4MzI2Mzg0fQ.YHpG_nNe3ssUgOT7JmYm4a5o8VV221oQZHM3F4bIzzM';

// Función para crear workflow de WhatsApp
async function createWhatsAppWorkflow() {
  const workflowData = {
    name: 'WhatsApp Message Handler',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      {
        parameters: {
          httpMethod: 'POST',
          path: 'whatsapp-webhook',
          responseMode: 'responseNode',
          options: {}
        },
        id: 'webhook-whatsapp',
        name: 'WhatsApp Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300],
        webhookId: 'whatsapp-webhook'
      },
      {
        parameters: {
          conditions: {
            options: {
              caseSensitive: true,
              leftValue: '',
              typeValidation: 'strict'
            },
            conditions: [
              {
                id: 'condition1',
                leftValue: '={{ $json.type }}',
                rightValue: 'message',
                operator: {
                  type: 'string',
                  operation: 'equals'
                }
              }
            ],
            combinator: 'and'
          },
          options: {}
        },
        id: 'if-message',
        name: 'Es Mensaje?',
        type: 'n8n-nodes-base.if',
        typeVersion: 2,
        position: [460, 300]
      },
      {
        parameters: {
          url: 'http://backend:3001/api/webhook/whatsapp',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'phone',
                value: '={{ $json.from }}'
              },
              {
                name: 'message',
                value: '={{ $json.body }}'
              },
              {
                name: 'timestamp',
                value: '={{ $json.timestamp }}'
              }
            ]
          },
          options: {}
        },
        id: 'send-to-backend',
        name: 'Enviar a Backend',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [680, 200]
      },
      {
        parameters: {
          respondWith: 'json',
          responseBody: '{\n  "status": "received",\n  "message": "Mensaje procesado correctamente"\n}'
        },
        id: 'respond-success',
        name: 'Responder Éxito',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [900, 200]
      },
      {
        parameters: {
          respondWith: 'json',
          responseBody: '{\n  "status": "ignored",\n  "message": "Tipo de mensaje no soportado"\n}'
        },
        id: 'respond-ignored',
        name: 'Responder Ignorado',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [680, 400]
      }
    ],
    connections: {
      'WhatsApp Webhook': {
        main: [
          [
            {
              node: 'Es Mensaje?',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Es Mensaje?': {
        main: [
          [
            {
              node: 'Enviar a Backend',
              type: 'main',
              index: 0
            }
          ],
          [
            {
              node: 'Responder Ignorado',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Enviar a Backend': {
        main: [
          [
            {
              node: 'Responder Éxito',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };

  try {
    const response = await axios.post(`${N8N_BASE_URL}/api/v1/workflows`, workflowData, {
      headers: {
        'Content-Type': 'application/json',
        'X-N8N-API-KEY': N8N_API_KEY
      }
    });

    console.log('✅ Workflow de WhatsApp creado exitosamente');
    console.log(`   ID: ${response.data.id}`);
    console.log(`   Webhook URL: ${N8N_BASE_URL}/webhook/whatsapp-webhook`);

    return response.data;
  } catch (error) {
    console.error('❌ Error creando workflow de WhatsApp:', error.response?.data || error.message);
    throw error;
  }
}

// Función para activar workflow
async function activateWorkflow(workflowId) {
  try {
    await axios.patch(`${N8N_BASE_URL}/api/v1/workflows/${workflowId}`, {
      active: true
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-N8N-API-KEY': N8N_API_KEY
      }
    });

    console.log(`✅ Workflow ${workflowId} activado`);
  } catch (error) {
    console.error(`❌ Error activando workflow ${workflowId}:`, error.response?.data || error.message);
  }
}

// Función principal
async function createBasicWorkflows() {
  try {
    console.log('=== Creando Workflows Básicos para n8n ===');

    // 1. Crear workflow de WhatsApp
    console.log('\n1. Creando workflow de WhatsApp...');
    const whatsappWorkflow = await createWhatsAppWorkflow();

    // 2. Activar workflow
    console.log('\n2. Activando workflow...');
    await activateWorkflow(whatsappWorkflow.id);

    console.log('\n=== CONFIGURACIÓN COMPLETADA ===');
    console.log('✅ Workflows básicos creados y activados');
    console.log('\n📋 URLs importantes:');
    console.log(`   WhatsApp Webhook: ${N8N_BASE_URL}/webhook/whatsapp-webhook`);
    console.log(`   n8n Editor: ${N8N_BASE_URL}`);

    return {
      whatsappWorkflow: whatsappWorkflow.id,
      webhookUrl: `${N8N_BASE_URL}/webhook/whatsapp-webhook`
    };

  } catch (error) {
    console.error('Error en la configuración:', error);
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createBasicWorkflows()
    .then((result) => {
      console.log('\n🎉 Workflows creados exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error creando workflows:', error);
      process.exit(1);
    });
}

module.exports = { createBasicWorkflows };
